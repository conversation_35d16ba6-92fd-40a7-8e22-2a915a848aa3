import 'dart:io';
import 'dart:convert';

void main(List<String> args) async {
  print('🔄 Flutter Opacity to Alpha Converter');
  print('=====================================');

  // Get the target directory from command line args or use current directory
  String targetDir = args.isNotEmpty ? args[0] : '.';

  if (!Directory(targetDir).existsSync()) {
    print('❌ Directory "$targetDir" does not exist');
    exit(1);
  }

  print('📁 Scanning directory: $targetDir');

  // Find all Dart files recursively
  List<File> dartFiles = await findDartFiles(targetDir);

  if (dartFiles.isEmpty) {
    print('ℹ️  No Dart files found in the specified directory');
    return;
  }

  print('📄 Found ${dartFiles.length} Dart files');

  int totalReplacements = 0;
  int modifiedFiles = 0;

  // Process each Dart file
  for (File file in dartFiles) {
    int replacements = await processFile(file);
    if (replacements > 0) {
      modifiedFiles++;
      totalReplacements += replacements;
      print('✅ ${file.path}: $replacements replacements');
    }
  }

  print('\n🎉 Conversion completed!');
  print('📊 Summary:');
  print('   - Files processed: ${dartFiles.length}');
  print('   - Files modified: $modifiedFiles');
  print('   - Total replacements: $totalReplacements');
}

/// Find all Dart files recursively in the given directory
Future<List<File>> findDartFiles(String dirPath) async {
  List<File> dartFiles = [];
  Directory dir = Directory(dirPath);

  await for (FileSystemEntity entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      // Skip generated files and build directories
      if (!entity.path.contains('/.dart_tool/') &&
          !entity.path.contains('/build/') &&
          !entity.path.contains('.g.dart') &&
          !entity.path.contains('.freezed.dart') &&
          !entity.path.contains('.gr.dart')) {
        dartFiles.add(entity);
      }
    }
  }

  return dartFiles;
}

/// Process a single Dart file and replace .withOpacity() with .withAlpha()
Future<int> processFile(File file) async {
  try {
    String content = await file.readAsString();
    String originalContent = content;

    // Regular expression to match .withAlpha(((value) * 255).round()) calls
    // This handles various formats: .withAlpha(128), .withAlpha(((opacity) * 255).round()), etc.
    RegExp opacityRegex = RegExp(
      r'\.withOpacity\s*\(\s*([^)]+)\s*\)',
      multiLine: true,
    );

    int replacementCount = 0;

    // Replace all matches
    content = content.replaceAllMapped(opacityRegex, (Match match) {
      String opacityValue = match.group(1)!.trim();
      String alphaValue = convertOpacityToAlpha(opacityValue);
      replacementCount++;
      return '.withAlpha($alphaValue)';
    });

    // Write back to file if changes were made
    if (content != originalContent) {
      await file.writeAsString(content);
    }

    return replacementCount;
  } catch (e) {
    print('❌ Error processing ${file.path}: $e');
    return 0;
  }
}

/// Convert opacity value (0.0-1.0) to alpha value (0-255)
String convertOpacityToAlpha(String opacityValue) {
  // Handle different types of opacity values

  // Check if it's a direct numeric value (e.g., "0.5", "1.0", "0")
  double? directValue = double.tryParse(opacityValue);
  if (directValue != null) {
    int alphaValue = (directValue * 255).round();
    return alphaValue.toString();
  }

  // Check for common opacity constants and convert them
  Map<String, String> commonOpacities = {
    '0.0': '0',
    '0.1': '26',
    '0.2': '51',
    '0.3': '77',
    '0.4': '102',
    '0.5': '128',
    '0.6': '153',
    '0.7': '179',
    '0.8': '204',
    '0.9': '230',
    '1.0': '255',
  };

  if (commonOpacities.containsKey(opacityValue)) {
    return commonOpacities[opacityValue]!;
  }

  // For variables or expressions, wrap them in a conversion formula
  // This handles cases like: .withAlpha(((opacity) * 255).round()) -> .withAlpha((opacity * 255).round())
  return '(($opacityValue) * 255).round()';
}

/// Utility function to create a backup of files before modification
Future<void> createBackup(String dirPath) async {
  String backupDir = '$dirPath/backup_${DateTime.now().millisecondsSinceEpoch}';
  Directory(backupDir).createSync(recursive: true);

  List<File> dartFiles = await findDartFiles(dirPath);

  for (File file in dartFiles) {
    String relativePath = file.path.replaceFirst('$dirPath/', '');
    String backupPath = '$backupDir/$relativePath';

    // Create directory structure in backup
    Directory(backupPath).parent.createSync(recursive: true);

    // Copy file to backup
    await file.copy(backupPath);
  }

  print('📋 Backup created at: $backupDir');
}

/// Preview mode - shows what would be changed without actually modifying files
Future<void> previewChanges(String targetDir) async {
  print('👀 Preview Mode - No files will be modified');
  print('==========================================');

  List<File> dartFiles = await findDartFiles(targetDir);

  for (File file in dartFiles) {
    String content = await file.readAsString();
    RegExp opacityRegex = RegExp(
      r'\.withOpacity\s*\(\s*([^)]+)\s*\)',
      multiLine: true,
    );

    Iterable<RegExpMatch> matches = opacityRegex.allMatches(content);

    if (matches.isNotEmpty) {
      print('\n📄 ${file.path}:');
      for (RegExpMatch match in matches) {
        String opacityValue = match.group(1)!.trim();
        String alphaValue = convertOpacityToAlpha(opacityValue);
        print('   .withAlpha((($opacityValue) * 255).round()) → .withAlpha($alphaValue)');
      }
    }
  }
}
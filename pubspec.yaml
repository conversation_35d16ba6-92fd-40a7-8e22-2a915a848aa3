name: dateme
description: "A new Dating App."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  injectable: ^2.5.0
  get_it: ^8.0.3
  intl:

  #Local Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  #State Management
  flutter_riverpod: ^2.6.1

  #UI
  flutter_screenutil: ^5.9.3
  responsive_builder: ^0.7.1
  shimmer: ^3.0.0
  flutter_launcher_icons: ^0.14.3
  loading_animation_widget: ^1.3.0
  flutter_svg: ^2.0.17
  stylish_bottom_bar: ^1.1.1
  lottie: ^3.3.1
  carousel_slider: ^5.0.0
  animated_text_kit: ^4.2.2
  cached_network_image: ^3.4.1
  flutter_card_swiper: ^7.0.2
  http_parser: ^4.0.2
  razorpay_flutter: ^1.4.0
  badges: ^3.1.2

  #Network
  dio: ^5.8.0+1
  firebase_core: ^3.13.1
  firebase_messaging: ^15.2.6
  firebase_analytics: ^11.4.6
  firebase_crashlytics: ^4.3.6
  flutter_local_notifications:

  # Google map
  google_maps_flutter: ^2.12.1
  geolocator: 13.0.2
  location:
  geocoding:

  # Pickers
  image_picker:
  image_cropper:
  crop_your_image:
  #  camerawesome: 2.4.0
  fluttertoast: 8.2.10

  # Webview
  webview_flutter:

  # Selfie
  camera: 0.11.1
  #  google_mlkit_face_detection:
  image: ^4.5.4

  agora_chat_sdk:


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  injectable_generator: ^2.6.2
  flutter_gen_runner: ^5.8.0
  lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package
  assets:
    - assets/images/
    - assets/fonts/
    - assets/svgs/
    - assets/animation/


  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  fonts:
    - family: Arima
      fonts:
        - asset: assets/fonts/Arima-Bold.ttf
        - asset: assets/fonts/Arima-ExtraLight.ttf
        - asset: assets/fonts/Arima-Light.ttf
        - asset: assets/fonts/Arima-Medium.ttf
        - asset: assets/fonts/Arima-Regular.ttf
        - asset: assets/fonts/Arima-SemiBold.ttf
        - asset: assets/fonts/Arima-Thin.ttf


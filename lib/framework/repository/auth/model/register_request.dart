// To parse this JSON data, do
//
//     final registerRequest = registerRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'register_request.g.dart';

RegisterRequest registerRequestFromJson(String str) => RegisterRequest.fromJson(json.decode(str));

String registerRequestToJson(RegisterRequest data) => json.encode(data.toJson());

@JsonSerializable()
class RegisterRequest {
  @Json<PERSON>ey(name: "email")
  String? email;
  @<PERSON><PERSON><PERSON><PERSON>(name: "password")
  String? password;
  @<PERSON><PERSON><PERSON><PERSON>(name: "full_name")
  String? fullName;
  @J<PERSON><PERSON>ey(name: "date_of_birth")
  String? dateOfBirth;
  @JsonKey(name: "gender")
  String? gender;
  @J<PERSON><PERSON><PERSON>(name: "fcm_token")
  String? fcmToken;

  RegisterRequest({
    this.email,
    this.password,
    this.fullName,
    this.dateOfBirth,
    this.gender,
    this.fcmToken,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

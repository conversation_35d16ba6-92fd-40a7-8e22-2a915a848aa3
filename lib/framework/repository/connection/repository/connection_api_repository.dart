import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/framework/repository/auth/model/common_message_response.dart';
import 'package:dateme/framework/repository/connection/contract/connection_repository.dart';
import 'package:dateme/framework/repository/connection/model/incoming_request_response_model.dart';
import 'package:dateme/framework/repository/connection/model/respond_connection_response_model.dart';
import 'package:dateme/framework/repository/connection/model/send_connection_response_model.dart';
import 'package:dateme/framework/repository/connection/model/verify_payment_response_model.dart';
import 'package:dateme/framework/repository/recommendation/model/get_match_recommendation_list_response_model.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: ConnectionRepository, env: [production, development])
class ConnectionApiRepository extends ConnectionRepository {
  DioClient apiClient;

  ConnectionApiRepository(this.apiClient);

  @override
  Future sendConnectionAPI(Map<String, dynamic> map) async {
    try {
      Response? response = await apiClient.postRequest(ApiEndPoints.sendConnection, map);
      SendConnectionResponseModel responseModel = sendConnectionResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future respondConnectionAPI(Map<String, dynamic> map) async {
    try {
      Response? response = await apiClient.postRequest(ApiEndPoints.respondConnection, map);
      RespondConnectionResponseModel responseModel = respondConnectionResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future destroyConnectionAPI(Map<String, dynamic> map) async {
    try {
      Response? response = await apiClient.postRequest(ApiEndPoints.destroyConnection, map);
      GetMatchRecommendationListResponseModel responseModel = getMatchRecommendationListResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }


  @override
  Future incomingConnectionRequestAPI() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.incomingConnectionRequest);
      IncomingRequestResponseModel responseModel = incomingRequestResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future outgoingConnectionRequestsAPI() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.outgoingConnectionRequests);
      IncomingRequestResponseModel responseModel = incomingRequestResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future myConnectionRequestsAPI() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.myConnectionRequests);
      IncomingRequestResponseModel responseModel = incomingRequestResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}

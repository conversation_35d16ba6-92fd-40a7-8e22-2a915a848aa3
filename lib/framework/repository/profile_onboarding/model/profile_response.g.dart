// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profile_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProfileResponse _$ProfileResponseFromJson(
  Map<String, dynamic> json,
) => ProfileResponse(
  success: json['success'] as bool?,
  userHashId: json['user_hashid'] as String?,
  fullName: json['full_name'] as String?,
  dateOfBirth:
      json['date_of_birth'] == null
          ? null
          : DateTime.parse(json['date_of_birth'] as String),
  gender: json['gender'] as String?,
  maritalStatus: json['marital_status'] as String?,
  currentCity: json['current_city'] as String?,
  country: json['country'] as String?,
  bio: json['bio'] as String?,
  interests:
      (json['interests'] as List<dynamic>?)?.map((e) => e as String).toList(),
  languages:
      (json['languages'] as List<dynamic>?)?.map((e) => e as String).toList(),
  photos:
      (json['photos'] as List<dynamic>?)
          ?.map((e) => PhotoModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  dietaryPreference: json['dietary_preference'] as String?,
  smokingHabit: json['smoking_habit'] as String?,
  drinkingHabit: json['drinking_habit'] as String?,
  religion: json['religion'] as String?,
  caste: json['caste'] as String?,
  height: (json['height'] as num?)?.toInt(),
  fitnessLevel: json['fitness_level'] as String?,
  education: json['education'] as String?,
  occupation: json['occupation'] as String?,
  occupationDetails: json['occupation_details'] as String?,
  annualIncome: json['annual_income'],
  minAge: (json['min_age'] as num?)?.toInt(),
  maxAge: (json['max_age'] as num?)?.toInt(),
  minHeight: json['min_height'],
  maxHeight: json['max_height'],
  minIncome: json['min_income'],
  maxIncome: json['max_income'],
  preferredCities: json['preferred_cities'],
  preferredCountries: json['preferred_countries'],
  lookingFor: json['looking_for'] as String?,
  relationshipIntent: json['relationship_intent'] as String?,
  termsAccepted: json['terms_accepted'] as bool?,
  privacyPolicyAccepted: json['privacy_policy_accepted'] as bool?,
  kycConsent: json['kyc_consent'] as bool?,
  trustPledge: json['trust_pledge'],
  profileCompletion: (json['profile_completion'] as num?)?.toInt(),
  isVisible: json['is_visible'] as bool?,
  lastActive:
      json['last_active'] == null
          ? null
          : DateTime.parse(json['last_active'] as String),
  isMobileVerified: json['is_mobile_verified'] as bool?,
  isKycVerified: json['is_kyc_verified'] as bool?,
  selfieVerified: json['selfie_verified'] as bool?,
  trustBadge: json['trust_badge'] as bool?,
  isVerified: json['is_verified'],
  lastLogin: json['last_login'],
  dobMatch: json['dob_match'],
  nameMatch: json['name_match'],
  isEmailVerified: json['is_email_verified'] as bool?,
  openToMarriage: json['open_to_marriage'],
  lifestylePreference:
      (json['lifestyle_preference'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  preferredRadius: (json['preferred_radius'] as num?)?.toInt(),
  isMpin: json['is_mpin'] as bool?,
);

Map<String, dynamic> _$ProfileResponseToJson(ProfileResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'user_hashid': instance.userHashId,
      'full_name': instance.fullName,
      'date_of_birth': instance.dateOfBirth?.toIso8601String(),
      'gender': instance.gender,
      'marital_status': instance.maritalStatus,
      'current_city': instance.currentCity,
      'country': instance.country,
      'bio': instance.bio,
      'interests': instance.interests,
      'languages': instance.languages,
      'photos': instance.photos,
      'dietary_preference': instance.dietaryPreference,
      'smoking_habit': instance.smokingHabit,
      'drinking_habit': instance.drinkingHabit,
      'religion': instance.religion,
      'caste': instance.caste,
      'height': instance.height,
      'fitness_level': instance.fitnessLevel,
      'education': instance.education,
      'occupation': instance.occupation,
      'occupation_details': instance.occupationDetails,
      'annual_income': instance.annualIncome,
      'min_age': instance.minAge,
      'max_age': instance.maxAge,
      'min_height': instance.minHeight,
      'max_height': instance.maxHeight,
      'min_income': instance.minIncome,
      'max_income': instance.maxIncome,
      'preferred_cities': instance.preferredCities,
      'preferred_countries': instance.preferredCountries,
      'looking_for': instance.lookingFor,
      'relationship_intent': instance.relationshipIntent,
      'terms_accepted': instance.termsAccepted,
      'privacy_policy_accepted': instance.privacyPolicyAccepted,
      'kyc_consent': instance.kycConsent,
      'trust_pledge': instance.trustPledge,
      'profile_completion': instance.profileCompletion,
      'is_visible': instance.isVisible,
      'last_active': instance.lastActive?.toIso8601String(),
      'is_mobile_verified': instance.isMobileVerified,
      'is_kyc_verified': instance.isKycVerified,
      'selfie_verified': instance.selfieVerified,
      'trust_badge': instance.trustBadge,
      'is_verified': instance.isVerified,
      'last_login': instance.lastLogin,
      'dob_match': instance.dobMatch,
      'name_match': instance.nameMatch,
      'is_email_verified': instance.isEmailVerified,
      'open_to_marriage': instance.openToMarriage,
      'lifestyle_preference': instance.lifestylePreference,
      'preferred_radius': instance.preferredRadius,
      'is_mpin': instance.isMpin,
    };

PhotoModel _$PhotoModelFromJson(Map<String, dynamic> json) => PhotoModel(
  mediaHashid: json['media_hashid'] as String?,
  mediaUrl: json['media_url'] as String?,
  mediaType: json['media_type'] as String?,
  isPrimary: json['is_primary'] as bool?,
  createdAt: json['created_at'] as String?,
  updatedAt: json['updated_at'] as String?,
  isActive: json['is_active'] as bool?,
);

Map<String, dynamic> _$PhotoModelToJson(PhotoModel instance) =>
    <String, dynamic>{
      'media_hashid': instance.mediaHashid,
      'media_url': instance.mediaUrl,
      'media_type': instance.mediaType,
      'is_primary': instance.isPrimary,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'is_active': instance.isActive,
    };

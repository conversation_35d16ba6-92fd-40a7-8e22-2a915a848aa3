// To parse this JSON data, do
//
//     final profileResponse = profileResponseFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'profile_response.g.dart';

ProfileResponse profileResponseFromJson(String str) => ProfileResponse.fromJson(json.decode(str));

String profileResponseToJson(ProfileResponse data) => json.encode(data.toJson());

@JsonSerializable()
class ProfileResponse {
  @JsonKey(name: 'success')
  bool? success;

  @Json<PERSON>ey(name: 'user_hashid')
  final String? userHashId;

  @Json<PERSON>ey(name: 'full_name')
  final String? fullName;

  @Json<PERSON>ey(name: 'date_of_birth')
  final DateTime? dateOfBirth;

  @JsonKey(name: 'gender')
  final String? gender;

  @Json<PERSON>ey(name: 'marital_status')
  final String? maritalStatus;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'current_city')
  final String? currentCity;

  @Json<PERSON>ey(name: 'country')
  final String? country;

  @JsonKey(name: 'bio')
  final String? bio;

  @JsonKey(name: 'interests')
  final List<String>? interests;

  @Json<PERSON>ey(name: 'languages')
  final List<String>? languages;

  @JsonKey(name: 'photos')
  final List<PhotoModel>? photos;

  @JsonKey(name: 'dietary_preference')
  final String? dietaryPreference;

  @JsonKey(name: 'smoking_habit')
  final String? smokingHabit;

  @JsonKey(name: 'drinking_habit')
  final String? drinkingHabit;

  @JsonKey(name: 'religion')
  final String? religion;

  @JsonKey(name: 'caste')
  final String? caste;

  @JsonKey(name: 'height')
  final int? height;

  @JsonKey(name: 'fitness_level')
  final String? fitnessLevel;

  @JsonKey(name: 'education')
  final String? education;

  @JsonKey(name: 'occupation')
  final String? occupation;

  @JsonKey(name: 'occupation_details')
  final String? occupationDetails;

  @JsonKey(name: 'annual_income')
  final dynamic annualIncome;

  @JsonKey(name: 'min_age')
  final int? minAge;

  @JsonKey(name: 'max_age')
  final int? maxAge;

  @JsonKey(name: 'min_height')
  final dynamic minHeight;

  @JsonKey(name: 'max_height')
  final dynamic maxHeight;

  @JsonKey(name: 'min_income')
  final dynamic minIncome;

  @JsonKey(name: 'max_income')
  final dynamic maxIncome;

  @JsonKey(name: 'preferred_cities')
  final dynamic preferredCities;

  @JsonKey(name: 'preferred_countries')
  final dynamic preferredCountries;

  @JsonKey(name: 'looking_for')
  final String? lookingFor;

  @JsonKey(name: 'relationship_intent')
  final String? relationshipIntent;

  @JsonKey(name: 'terms_accepted')
  final bool? termsAccepted;

  @JsonKey(name: 'privacy_policy_accepted')
  final bool? privacyPolicyAccepted;

  @JsonKey(name: 'kyc_consent')
  final bool? kycConsent;

  @JsonKey(name: 'trust_pledge')
  final dynamic trustPledge;

  @JsonKey(name: 'profile_completion')
  final int? profileCompletion;

  @JsonKey(name: 'is_visible')
  final bool? isVisible;

  @JsonKey(name: 'last_active')
  final DateTime? lastActive;

  @JsonKey(name: 'is_mobile_verified')
  final bool? isMobileVerified;

  @JsonKey(name: 'is_kyc_verified')
  final bool? isKycVerified;

  @JsonKey(name: 'selfie_verified')
  final bool? selfieVerified;

  @JsonKey(name: 'trust_badge')
  final bool? trustBadge;

  @JsonKey(name: 'is_verified')
  final dynamic isVerified;

  @JsonKey(name: 'last_login')
  final dynamic lastLogin;

  @JsonKey(name: 'dob_match')
  final dynamic dobMatch;

  @JsonKey(name: 'name_match')
  final dynamic nameMatch;

  @JsonKey(name: 'is_email_verified')
  final bool? isEmailVerified;

  @JsonKey(name: 'open_to_marriage')
  final dynamic openToMarriage;

  @JsonKey(name: 'lifestyle_preference')
  List<String>? lifestylePreference;

  @JsonKey(name: 'preferred_radius')
  final int? preferredRadius;

  @JsonKey(name: 'is_mpin')
  final bool? isMpin;

  ProfileResponse({
    this.success,
    this.userHashId,
    this.fullName,
    this.dateOfBirth,
    this.gender,
    this.maritalStatus,
    this.currentCity,
    this.country,
    this.bio,
    this.interests,
    this.languages,
    this.photos,
    this.dietaryPreference,
    this.smokingHabit,
    this.drinkingHabit,
    this.religion,
    this.caste,
    this.height,
    this.fitnessLevel,
    this.education,
    this.occupation,
    this.occupationDetails,
    this.annualIncome,
    this.minAge,
    this.maxAge,
    this.minHeight,
    this.maxHeight,
    this.minIncome,
    this.maxIncome,
    this.preferredCities,
    this.preferredCountries,
    this.lookingFor,
    this.relationshipIntent,
    this.termsAccepted,
    this.privacyPolicyAccepted,
    this.kycConsent,
    this.trustPledge,
    this.profileCompletion,
    this.isVisible,
    this.lastActive,
    this.isMobileVerified,
    this.isKycVerified,
    this.selfieVerified,
    this.trustBadge,
    this.isVerified,
    this.lastLogin,
    this.dobMatch,
    this.nameMatch,
    this.isEmailVerified,
    this.openToMarriage,
    this.lifestylePreference,
    this.preferredRadius,
    this.isMpin,
  });

  factory ProfileResponse.fromJson(Map<String, dynamic> json) => _$ProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ProfileResponseToJson(this);
}

@JsonSerializable()
class PhotoModel {
  @JsonKey(name: 'media_hashid')
  String? mediaHashid;
  @JsonKey(name: 'media_url')
  String? mediaUrl;
  @JsonKey(name: 'media_type')
  String? mediaType;
  @JsonKey(name: 'is_primary')
  bool? isPrimary;
  @JsonKey(name: 'created_at')
  String? createdAt;
  @JsonKey(name: 'updated_at')
  String? updatedAt;
  @JsonKey(name: 'is_active')
  bool? isActive;

  PhotoModel({
    this.mediaHashid,
    this.mediaUrl,
    this.mediaType,
    this.isPrimary,
    this.createdAt,
    this.updatedAt,
    this.isActive,
  });

  factory PhotoModel.fromJson(Map<String, dynamic> json) => _$PhotoModelFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoModelToJson(this);
}

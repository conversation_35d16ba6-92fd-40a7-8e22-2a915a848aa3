import 'package:dio/dio.dart';

abstract class ProfileRepository {
  Future getProfile();

  Future updateProfile(Map<String, dynamic> request);

  Future createProfile(Map<String, dynamic> request);

  Future uploadMediaProfile(FormData request);

  Future sendOTPMobile(Map<String, dynamic> request);

  Future verifyOTPMobile(Map<String, dynamic> request);

  Future aadhaarOTPMobile(Map<String, dynamic> request);

  Future interestsSuggestionApi();

  Future updateLocationAPI(Map<String, dynamic> request);

  /// Delete Media Api
  Future deleteMediaAPI(String mediaHashId);

  Future setProfilePicture(String mediaHashId);
}

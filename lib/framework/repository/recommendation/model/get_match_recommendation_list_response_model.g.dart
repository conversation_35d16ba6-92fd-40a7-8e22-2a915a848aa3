// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_match_recommendation_list_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetMatchRecommendationListResponseModel
_$GetMatchRecommendationListResponseModelFromJson(Map<String, dynamic> json) =>
    GetMatchRecommendationListResponseModel(
      results:
          (json['results'] as List<dynamic>?)
              ?.map(
                (e) => RecommendationResult.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      pagination:
          json['pagination'] == null
              ? null
              : Pagination.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetMatchRecommendationListResponseModelToJson(
  GetMatchRecommendationListResponseModel instance,
) => <String, dynamic>{
  'results': instance.results,
  'pagination': instance.pagination,
};

Pagination _$PaginationFromJson(Map<String, dynamic> json) => Pagination(
  currentPage: (json['current_page'] as num?)?.toInt(),
  totalPages: (json['total_pages'] as num?)?.toInt(),
  totalItems: (json['total_items'] as num?)?.toInt(),
);

Map<String, dynamic> _$PaginationToJson(Pagination instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'total_pages': instance.totalPages,
      'total_items': instance.totalItems,
    };

RecommendationResult _$RecommendationResultFromJson(
  Map<String, dynamic> json,
) => RecommendationResult(
  userHashid: json['user_hashid'] as String?,
  mediaList:
      (json['media_list'] as List<dynamic>?)
          ?.map((e) => MediaList.fromJson(e as Map<String, dynamic>))
          .toList(),
  selfieVerified: json['selfie_verified'] as bool?,
  trustBadge: json['trust_badge'] as bool?,
  isKycVerified: json['is_kyc_verified'] as bool?,
  firstName: json['first_name'] as String?,
  age: (json['age'] as num?)?.toInt(),
  currentCity: json['current_city'] as String?,
  lifestylePreference:
      (json['lifestyle_preference'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
  relationshipIntent: json['relationship_intent'] as String?,
  openToMarriage: json['open_to_marriage'],
  bio: json['bio'] as String?,
  lookingFor: json['looking_for'],
  interests:
      (json['interests'] as List<dynamic>?)?.map((e) => e as String).toList(),
  languages:
      (json['languages'] as List<dynamic>?)?.map((e) => e as String).toList(),
  fitnessLevel: json['fitness_level'] as String?,
  hobbies: json['hobbies'],
  dietaryPreference: json['dietary_preference'] as String?,
  smokingHabit: json['smoking_habit'] as String?,
  drinkingHabit: json['drinking_habit'] as String?,
  religion: json['religion'] as String?,
  caste: json['caste'] as String?,
  education: json['education'] as String?,
  occupation: json['occupation'] as String?,
  occupationDetails: json['occupation_details'] as String?,
  annualIncome: json['annual_income'] as String?,
  mobileVerified: json['mobile_verified'] as bool?,
  canMarkFavorite: json['can_mark_favorite'] as bool?,
  canSendConnectionRequest: json['can_send_connection_request'] as bool?,
  canReportProfile: json['can_report_profile'] as bool?,
  compatibilityScore: (json['compatibility_score'] as num?)?.toDouble(),
  isLiked: json['liked'] as bool?,
);

Map<String, dynamic> _$RecommendationResultToJson(
  RecommendationResult instance,
) => <String, dynamic>{
  'user_hashid': instance.userHashid,
  'media_list': instance.mediaList,
  'selfie_verified': instance.selfieVerified,
  'trust_badge': instance.trustBadge,
  'is_kyc_verified': instance.isKycVerified,
  'first_name': instance.firstName,
  'age': instance.age,
  'current_city': instance.currentCity,
  'lifestyle_preference': instance.lifestylePreference,
  'relationship_intent': instance.relationshipIntent,
  'open_to_marriage': instance.openToMarriage,
  'bio': instance.bio,
  'looking_for': instance.lookingFor,
  'interests': instance.interests,
  'languages': instance.languages,
  'fitness_level': instance.fitnessLevel,
  'hobbies': instance.hobbies,
  'dietary_preference': instance.dietaryPreference,
  'smoking_habit': instance.smokingHabit,
  'drinking_habit': instance.drinkingHabit,
  'religion': instance.religion,
  'caste': instance.caste,
  'education': instance.education,
  'occupation': instance.occupation,
  'occupation_details': instance.occupationDetails,
  'annual_income': instance.annualIncome,
  'mobile_verified': instance.mobileVerified,
  'can_mark_favorite': instance.canMarkFavorite,
  'can_send_connection_request': instance.canSendConnectionRequest,
  'can_report_profile': instance.canReportProfile,
  'compatibility_score': instance.compatibilityScore,
  'liked': instance.isLiked,
};

MediaList _$MediaListFromJson(Map<String, dynamic> json) => MediaList(
  mediaHashid: json['media_hashid'] as String?,
  mediaUrl: json['media_url'] as String?,
  mediaType: json['media_type'] as String?,
  isPrimary: json['is_primary'] as bool?,
  orderIndex: (json['order_index'] as num?)?.toInt(),
  isLiked: json['is_liked'] as bool?,
);

Map<String, dynamic> _$MediaListToJson(MediaList instance) => <String, dynamic>{
  'media_hashid': instance.mediaHashid,
  'media_url': instance.mediaUrl,
  'media_type': instance.mediaType,
  'is_primary': instance.isPrimary,
  'order_index': instance.orderIndex,
  'is_liked': instance.isLiked,
};

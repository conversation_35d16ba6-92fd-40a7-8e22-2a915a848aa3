// To parse this JSON data, do
//
//     final getMatchRecommendationListResponseModel = getMatchRecommendationListResponseModelFromJson(jsonString);

import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'get_match_recommendation_list_response_model.g.dart';

GetMatchRecommendationListResponseModel getMatchRecommendationListResponseModelFromJson(String str) =>
    GetMatchRecommendationListResponseModel.fromJson(json.decode(str));

String getMatchRecommendationListResponseModelToJson(GetMatchRecommendationListResponseModel data) =>
    json.encode(data.toJson());

@JsonSerializable()
class GetMatchRecommendationListResponseModel {
  @JsonKey(name: 'results')
  List<RecommendationResult>? results;
  @JsonKey(name: 'pagination')
  Pagination? pagination;

  GetMatchRecommendationListResponseModel({this.results, this.pagination});

  factory GetMatchRecommendationListResponseModel.fromJson(Map<String, dynamic> json) =>
      _$GetMatchRecommendationListResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetMatchRecommendationListResponseModelToJson(this);
}

@JsonSerializable()
class Pagination {
  @JsonKey(name: 'current_page')
  int? currentPage;
  @JsonKey(name: 'total_pages')
  int? totalPages;
  @JsonKey(name: 'total_items')
  int? totalItems;

  Pagination({this.currentPage, this.totalPages, this.totalItems});

  factory Pagination.fromJson(Map<String, dynamic> json) => _$PaginationFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationToJson(this);
}

@JsonSerializable()
class RecommendationResult {
  @JsonKey(name: 'user_hashid')
  String? userHashid;
  @JsonKey(name: 'media_list')
  List<MediaList>? mediaList;
  @JsonKey(name: 'selfie_verified')
  bool? selfieVerified;
  @JsonKey(name: 'trust_badge')
  bool? trustBadge;
  @JsonKey(name: 'is_kyc_verified')
  bool? isKycVerified;
  @JsonKey(name: 'first_name')
  String? firstName;
  @JsonKey(name: 'age')
  int? age;
  @JsonKey(name: 'current_city')
  String? currentCity;
  @JsonKey(name: "lifestyle_preference")
  List<String>? lifestylePreference;
  @JsonKey(name: "relationship_intent")
  String? relationshipIntent;
  @JsonKey(name: 'open_to_marriage')
  dynamic openToMarriage;
  @JsonKey(name: 'bio')
  String? bio;
  @JsonKey(name: 'looking_for')
  dynamic lookingFor;
  @JsonKey(name: 'interests')
  List<String>? interests;
  @JsonKey(name: 'languages')
  List<String>? languages;
  @JsonKey(name: 'fitness_level')
  String? fitnessLevel;
  @JsonKey(name: 'hobbies')
  dynamic hobbies;
  @JsonKey(name: 'dietary_preference')
  String? dietaryPreference;
  @JsonKey(name: 'smoking_habit')
  String? smokingHabit;
  @JsonKey(name: 'drinking_habit')
  String? drinkingHabit;
  @JsonKey(name: 'religion')
  String? religion;
  @JsonKey(name: 'caste')
  String? caste;
  @JsonKey(name: 'education')
  String? education;
  @JsonKey(name: 'occupation')
  String? occupation;
  @JsonKey(name: 'occupation_details')
  String? occupationDetails;
  @JsonKey(name: "annual_income")
  String? annualIncome;
  @JsonKey(name: "mobile_verified")
  bool? mobileVerified;
  @JsonKey(name: 'can_mark_favorite')
  bool? canMarkFavorite;
  @JsonKey(name: 'can_send_connection_request')
  bool? canSendConnectionRequest;
  @JsonKey(name: 'can_report_profile')
  bool? canReportProfile;
  @JsonKey(name: 'compatibility_score')
  double? compatibilityScore;
  @JsonKey(name: "liked")
  bool? isLiked;

  RecommendationResult({
    this.userHashid,
    this.mediaList,
    this.selfieVerified,
    this.trustBadge,
    this.isKycVerified,
    this.firstName,
    this.age,
    this.currentCity,
    this.lifestylePreference,
    this.relationshipIntent,
    this.openToMarriage,
    this.bio,
    this.lookingFor,
    this.interests,
    this.languages,
    this.fitnessLevel,
    this.hobbies,
    this.dietaryPreference,
    this.smokingHabit,
    this.drinkingHabit,
    this.religion,
    this.caste,
    this.education,
    this.occupation,
    this.occupationDetails,
    this.annualIncome,
    this.mobileVerified,
    this.canMarkFavorite,
    this.canSendConnectionRequest,
    this.canReportProfile,
    this.compatibilityScore,
    this.isLiked,
  });

  factory RecommendationResult.fromJson(Map<String, dynamic> json) => _$RecommendationResultFromJson(json);

  Map<String, dynamic> toJson() => _$RecommendationResultToJson(this);
}

@JsonSerializable()
class MediaList {
  @JsonKey(name: 'media_hashid')
  String? mediaHashid;
  @JsonKey(name: 'media_url')
  String? mediaUrl;
  @JsonKey(name: 'media_type')
  String? mediaType;
  @JsonKey(name: 'is_primary')
  bool? isPrimary;
  @JsonKey(name: 'order_index')
  int? orderIndex;
  @JsonKey(name: "is_liked")
  bool? isLiked;

  MediaList({
    this.mediaHashid,
    this.mediaUrl,
    this.mediaType,
    this.isPrimary,
    this.orderIndex,
    this.isLiked,
  });

  factory MediaList.fromJson(Map<String, dynamic> json) => _$MediaListFromJson(json);

  Map<String, dynamic> toJson() => _$MediaListToJson(this);
}

import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/framework/repository/like/contract/like_repository.dart';
import 'package:dateme/framework/repository/like/model/create_like_response_model.dart';
import 'package:dateme/framework/repository/like/model/get_like_list_response_model.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: LikeRepository, env: [production, development])
class LikeApiRepository extends LikeRepository {
  DioClient apiClient;

  LikeApiRepository(this.apiClient);

  @override
  Future createLikeAPI(Map<String, dynamic> map) async {
    try {
      Response? response = await apiClient.postRequest(ApiEndPoints.likeAPI, map);
      CreateLikeResponseModel responseModel = createLikeResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future removeLike(String targetId, String likeType) async {
    try {
      Response? response = await apiClient.deleteRequest(ApiEndPoints.removeLike(targetId, likeType), {});
      CreateLikeResponseModel responseModel = createLikeResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future likeGivenByUsersList(String likeType) async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.giveLike(likeType));
      GetLikeListResponseModel responseModel = getLikeListResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getLikesReceivedByUsersList(String likeType) async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.receivedLike(likeType));
      GetLikeListResponseModel responseModel = getLikeListResponseModelFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}

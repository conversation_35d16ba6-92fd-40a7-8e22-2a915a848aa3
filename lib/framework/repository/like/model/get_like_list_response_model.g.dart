// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_like_list_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetLikeListResponseModel _$GetLikeListResponseModelFromJson(
  Map<String, dynamic> json,
) => GetLikeListResponseModel(
  results:
      (json['results'] as List<dynamic>?)
          ?.map((e) => LikeModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  total: (json['total'] as num?)?.toInt(),
);

Map<String, dynamic> _$GetLikeListResponseModelToJson(
  GetLikeListResponseModel instance,
) => <String, dynamic>{'results': instance.results, 'total': instance.total};

LikeModel _$LikeModelFromJson(Map<String, dynamic> json) => LikeModel(
  likeHashid: json['like_hashid'] as String?,
  targetUserId: json['target_user_id'] as String?,
  likeType: json['like_type'] as String?,
  createdAt: json['created_at'] as String?,
  isActive: json['is_active'] as bool?,
  userInfo:
      json['user_info'] == null
          ? null
          : UserInfoModel.fromJson(json['user_info'] as Map<String, dynamic>),
);

Map<String, dynamic> _$LikeModelToJson(LikeModel instance) => <String, dynamic>{
  'like_hashid': instance.likeHashid,
  'target_user_id': instance.targetUserId,
  'like_type': instance.likeType,
  'created_at': instance.createdAt,
  'is_active': instance.isActive,
  'user_info': instance.userInfo,
};

UserInfoModel _$UserInfoModelFromJson(Map<String, dynamic> json) =>
    UserInfoModel(
      userHashid: json['user_hashid'] as String?,
      fullName: json['full_name'] as String?,
      profilePicture: json['profile_picture'] as String?,
      age: (json['age'] as num?)?.toInt(),
      gender: json['gender'] as String?,
      maritalStatus: json['marital_status'] as String?,
      currentCity: json['current_city'] as String?,
      country: json['country'] as String?,
      education: json['education'] as String?,
      occupation: json['occupation'] as String?,
      isVerified: json['is_verified'] as bool?,
      trustBadge: json['trust_badge'] as bool?,
    );

Map<String, dynamic> _$UserInfoModelToJson(UserInfoModel instance) =>
    <String, dynamic>{
      'user_hashid': instance.userHashid,
      'full_name': instance.fullName,
      'profile_picture': instance.profilePicture,
      'age': instance.age,
      'gender': instance.gender,
      'marital_status': instance.maritalStatus,
      'current_city': instance.currentCity,
      'country': instance.country,
      'education': instance.education,
      'occupation': instance.occupation,
      'is_verified': instance.isVerified,
      'trust_badge': instance.trustBadge,
    };

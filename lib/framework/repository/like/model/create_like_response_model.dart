// To parse this JSON data, do
//
//     final createLikeResponseModel = createLikeResponseModelFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'create_like_response_model.g.dart';

CreateLikeResponseModel createLikeResponseModelFromJson(String str) => CreateLikeResponseModel.fromJson(json.decode(str));

String createLikeResponseModelToJson(CreateLikeResponseModel data) => json.encode(data.toJson());

@JsonSerializable()
class CreateLikeResponseModel {
  @JsonKey(name: 'like_hashid')
  String? likeHashid;
  @JsonKey(name: 'target_user_id')
  String? targetUserId;
  @JsonKey(name: 'like_type')
  String? likeType;
  @JsonKey(name: 'created_at')
  DateTime? createdAt;
  @Json<PERSON>ey(name: 'is_active')
  bool? isActive;
  @Json<PERSON>ey(name: 'user_info')
  UserInfo? userInfo;

  CreateLikeResponseModel({
    this.likeHashid,
    this.targetUserId,
    this.likeType,
    this.createdAt,
    this.isActive,
    this.userInfo,
  });

  factory CreateLikeResponseModel.fromJson(Map<String, dynamic> json) => _$CreateLikeResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreateLikeResponseModelToJson(this);
}

@JsonSerializable()
class UserInfo {
  @JsonKey(name: 'user_hashid')
  String? userHashid;
  @JsonKey(name: 'full_name')
  String? fullName;
  @JsonKey(name: 'profile_picture')
  String? profilePicture;
  @JsonKey(name: 'age')
  int? age;
  @JsonKey(name: 'gender')
  String? gender;
  @JsonKey(name: 'marital_status')
  String? maritalStatus;
  @JsonKey(name: 'current_city')
  String? currentCity;
  @JsonKey(name: 'country')
  String? country;
  @JsonKey(name: 'education')
  String? education;
  @JsonKey(name: 'occupation')
  String? occupation;
  @JsonKey(name: 'is_verified')
  bool? isVerified;
  @JsonKey(name: 'trust_badge')
  bool? trustBadge;

  UserInfo({
    this.userHashid,
    this.fullName,
    this.profilePicture,
    this.age,
    this.gender,
    this.maritalStatus,
    this.currentCity,
    this.country,
    this.education,
    this.occupation,
    this.isVerified,
    this.trustBadge,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) => _$UserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

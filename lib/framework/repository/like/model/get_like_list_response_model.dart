// To parse this JSON data, do
//
//     final getLikeListResponseModel = getLikeListResponseModelFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_like_list_response_model.g.dart';

GetLikeListResponseModel getLikeListResponseModelFromJson(String str) => GetLikeListResponseModel.fromJson(json.decode(str));

String getLikeListResponseModelToJson(GetLikeListResponseModel data) => json.encode(data.toJson());

@JsonSerializable()
class GetLikeListResponseModel {
  @JsonKey(name: "results")
  List<LikeModel>? results;
  @JsonKey(name: "total")
  int? total;

  GetLikeListResponseModel({
    this.results,
    this.total,
  });

  factory GetLikeListResponseModel.fromJson(Map<String, dynamic> json) => _$GetLikeListResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$GetLikeListResponseModelToJson(this);
}

@JsonSerializable()
class LikeModel {
  @JsonKey(name: "like_hashid")
  String? likeHashid;
  @JsonKey(name: "target_user_id")
  String? targetUserId;
  @JsonKey(name: "like_type")
  String? likeType;
  @JsonKey(name: "created_at")
  String? createdAt;
  @JsonKey(name: "is_active")
  bool? isActive;
  @JsonKey(name: "user_info")
  UserInfoModel? userInfo;

  LikeModel({
    this.likeHashid,
    this.targetUserId,
    this.likeType,
    this.createdAt,
    this.isActive,
    this.userInfo,
  });

  factory LikeModel.fromJson(Map<String, dynamic> json) => _$LikeModelFromJson(json);

  Map<String, dynamic> toJson() => _$LikeModelToJson(this);
}

@JsonSerializable()
class UserInfoModel {
  @JsonKey(name: "user_hashid")
  String? userHashid;
  @JsonKey(name: "full_name")
  String? fullName;
  @JsonKey(name: "profile_picture")
  String? profilePicture;
  @JsonKey(name: "age")
  int? age;
  @JsonKey(name: "gender")
  String? gender;
  @JsonKey(name: "marital_status")
  String? maritalStatus;
  @JsonKey(name: "current_city")
  String? currentCity;
  @JsonKey(name: "country")
  String? country;
  @JsonKey(name: "education")
  String? education;
  @JsonKey(name: "occupation")
  String? occupation;
  @JsonKey(name: "is_verified")
  bool? isVerified;
  @JsonKey(name: "trust_badge")
  bool? trustBadge;

  UserInfoModel({
    this.userHashid,
    this.fullName,
    this.profilePicture,
    this.age,
    this.gender,
    this.maritalStatus,
    this.currentCity,
    this.country,
    this.education,
    this.occupation,
    this.isVerified,
    this.trustBadge,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) => _$UserInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoModelToJson(this);
}

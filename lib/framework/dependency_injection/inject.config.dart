// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:dateme/framework/controller/auth/login_controller.dart'
    as _i863;
import 'package:dateme/framework/controller/auth/signup_controller.dart'
    as _i808;
import 'package:dateme/framework/controller/bottom_nav_controller.dart'
    as _i146;
import 'package:dateme/framework/controller/chat/chat_controller.dart' as _i161;
import 'package:dateme/framework/controller/cms/cms_controller.dart' as _i144;
import 'package:dateme/framework/controller/connection/connection_controller.dart'
    as _i701;
import 'package:dateme/framework/controller/dashboard/dashboard_controller.dart'
    as _i735;
import 'package:dateme/framework/controller/forgot_password/forgot_password_controller.dart'
    as _i181;
import 'package:dateme/framework/controller/mpin/mpin_controller.dart' as _i43;
import 'package:dateme/framework/controller/my_profile/my_profile_controller.dart'
    as _i270;
import 'package:dateme/framework/controller/other_profile/others_profile_controller.dart'
    as _i87;
import 'package:dateme/framework/controller/profile_onboarding/basic_profile_controller.dart'
    as _i892;
import 'package:dateme/framework/controller/profile_onboarding/complete_profile_onboarding_controller.dart'
    as _i1036;
import 'package:dateme/framework/controller/profile_onboarding/get_location_controller.dart'
    as _i433;
import 'package:dateme/framework/controller/profile_onboarding/identity_verification/mobile_verification_controller.dart'
    as _i193;
import 'package:dateme/framework/controller/profile_onboarding/more_about_you_controller.dart'
    as _i761;
import 'package:dateme/framework/controller/profile_onboarding/partner_preference_controller.dart'
    as _i323;
import 'package:dateme/framework/controller/profile_onboarding/photo_upload_controller.dart'
    as _i106;
import 'package:dateme/framework/controller/profile_onboarding/policy_confirmation_controller.dart'
    as _i147;
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart'
    as _i820;
import 'package:dateme/framework/controller/recommendation/recommendation_screen_controller.dart'
    as _i241;
import 'package:dateme/framework/controller/wallet/wallet_screen_controller.dart'
    as _i301;
import 'package:dateme/framework/dependency_injection/modules/dio_api_client.dart'
    as _i74;
import 'package:dateme/framework/dependency_injection/modules/dio_looger_module.dart'
    as _i597;
import 'package:dateme/framework/provider/network/dio/dio_client.dart' as _i843;
import 'package:dateme/framework/provider/network/dio/dio_logger.dart' as _i84;
import 'package:dateme/framework/provider/network/network.dart' as _i568;
import 'package:dateme/framework/repository/auth/contract/auth_repository.dart'
    as _i860;
import 'package:dateme/framework/repository/auth/repository/auth_api_repository.dart'
    as _i796;
import 'package:dateme/framework/repository/connection/contract/connection_repository.dart'
    as _i752;
import 'package:dateme/framework/repository/connection/repository/connection_api_repository.dart'
    as _i677;
import 'package:dateme/framework/repository/like/contract/like_repository.dart'
    as _i1072;
import 'package:dateme/framework/repository/like/repository/like_api_repository.dart'
    as _i548;
import 'package:dateme/framework/repository/mpin/contract/mpin_repository.dart'
    as _i867;
import 'package:dateme/framework/repository/mpin/repository/mpin_api_repository.dart'
    as _i387;
import 'package:dateme/framework/repository/profile_onboarding/contract/profile_repository.dart'
    as _i908;
import 'package:dateme/framework/repository/profile_onboarding/repository/profile_api_repository.dart'
    as _i873;
import 'package:dateme/framework/repository/recommendation/contract/recommendation_repository.dart'
    as _i724;
import 'package:dateme/framework/repository/recommendation/repository/recommendation_api_repository.dart'
    as _i42;
import 'package:dateme/framework/repository/wallet/contract/wallet_repository.dart'
    as _i11;
import 'package:dateme/framework/repository/wallet/repository/wallet_api_repository.dart'
    as _i766;
import 'package:dateme/framework/utils/anim/custom_animation_controller.dart'
    as _i578;
import 'package:dateme/ui/profile_onboarding/complete_profile_onboarding_controller.dart'
    as _i650;
import 'package:dateme/ui/routing/delegate.dart' as _i619;
import 'package:dateme/ui/routing/navigation_stack_item.dart' as _i560;
import 'package:dateme/ui/routing/parser.dart' as _i4;
import 'package:dateme/ui/routing/stack.dart' as _i66;
import 'package:flutter/material.dart' as _i409;
import 'package:flutter_riverpod/flutter_riverpod.dart' as _i729;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

const String _development = 'development';
const String _production = 'production';

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final dioLoggerModule = _$DioLoggerModule();
    final networkModule = _$NetworkModule();
    gh.factory<_i650.CompleteProfileOnboardingController>(
      () => _i650.CompleteProfileOnboardingController(),
    );
    gh.factory<_i578.CustomAnimationController>(
      () => _i578.CustomAnimationController(),
    );
    gh.factory<_i87.OthersProfileController>(
      () => _i87.OthersProfileController(),
    );
    gh.factory<_i161.ChatController>(() => _i161.ChatController());
    gh.factory<_i147.PolicyController>(() => _i147.PolicyController());
    gh.factory<_i1036.CompleteProfileOnboardingController>(
      () => _i1036.CompleteProfileOnboardingController(),
    );
    gh.factory<_i106.PhotoUploadController>(
      () => _i106.PhotoUploadController(),
    );
    gh.factory<_i433.GetLocationController>(
      () => _i433.GetLocationController(),
    );
    gh.factory<_i892.BasicProfileController>(
      () => _i892.BasicProfileController(),
    );
    gh.factory<_i323.PartnerPreferenceController>(
      () => _i323.PartnerPreferenceController(),
    );
    gh.factory<_i735.DashboardController>(() => _i735.DashboardController());
    gh.factory<_i144.CmsController>(() => _i144.CmsController());
    gh.factory<_i146.BottomNavController>(() => _i146.BottomNavController());
    gh.factoryParam<
      _i66.NavigationStack,
      List<_i560.NavigationStackItem>,
      dynamic
    >((items, _) => _i66.NavigationStack(items));
    gh.lazySingleton<_i84.DioLogger>(
      () => dioLoggerModule.getDioLogger(),
      registerFor: {_development, _production},
    );
    gh.lazySingleton<_i843.DioClient>(
      () => networkModule.getProductionDioClient(gh<_i84.DioLogger>()),
      registerFor: {_production},
    );
    gh.factoryParam<
      _i4.MainRouterInformationParser,
      _i729.WidgetRef,
      _i409.BuildContext
    >((ref, context) => _i4.MainRouterInformationParser(ref, context));
    gh.lazySingleton<_i843.DioClient>(
      () => networkModule.getDebugDioClient(gh<_i84.DioLogger>()),
      registerFor: {_development},
    );
    gh.lazySingleton<_i1072.LikeRepository>(
      () => _i548.LikeApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.lazySingleton<_i724.RecommendationRepository>(
      () => _i42.RecommendationApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.lazySingleton<_i908.ProfileRepository>(
      () => _i873.ProfileApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.lazySingleton<_i860.AuthRepository>(
      () => _i796.AuthApiRepository(gh<_i843.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.lazySingleton<_i867.MPinRepository>(
      () => _i387.MPinApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.lazySingleton<_i11.WalletRepository>(
      () => _i766.WalletApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.factory<_i301.WalletController>(
      () => _i301.WalletController(gh<_i11.WalletRepository>()),
    );
    gh.lazySingleton<_i752.ConnectionRepository>(
      () => _i677.ConnectionApiRepository(gh<_i568.DioClient>()),
      registerFor: {_production, _development},
    );
    gh.factoryParam<_i619.MainRouterDelegate, _i66.NavigationStack, dynamic>(
      (stack, _) => _i619.MainRouterDelegate(stack),
    );
    gh.factory<_i701.ConnectionController>(
      () => _i701.ConnectionController(
        gh<_i752.ConnectionRepository>(),
        gh<_i1072.LikeRepository>(),
      ),
    );
    gh.factory<_i181.ForgotPasswordController>(
      () => _i181.ForgotPasswordController(gh<_i860.AuthRepository>()),
    );
    gh.factory<_i808.SignupController>(
      () => _i808.SignupController(gh<_i860.AuthRepository>()),
    );
    gh.factory<_i863.LoginController>(
      () => _i863.LoginController(gh<_i860.AuthRepository>()),
    );
    gh.factory<_i241.RecommendationScreenController>(
      () => _i241.RecommendationScreenController(
        gh<_i724.RecommendationRepository>(),
      ),
    );
    gh.factory<_i43.MpinController>(
      () => _i43.MpinController(gh<_i867.MPinRepository>()),
    );
    gh.factory<_i761.MoreAboutYouController>(
      () => _i761.MoreAboutYouController(gh<_i908.ProfileRepository>()),
    );
    gh.factory<_i193.MobileVerificationController>(
      () => _i193.MobileVerificationController(gh<_i908.ProfileRepository>()),
    );
    gh.factory<_i820.ProfileOnboardingController>(
      () => _i820.ProfileOnboardingController(gh<_i908.ProfileRepository>()),
    );
    gh.factory<_i270.MyProfileController>(
      () => _i270.MyProfileController(gh<_i908.ProfileRepository>()),
    );
    return this;
  }
}

class _$DioLoggerModule extends _i597.DioLoggerModule {}

class _$NetworkModule extends _i74.NetworkModule {}

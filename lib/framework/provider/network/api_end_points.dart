class ApiEndPoints {
  /*
  * ----- Api status
  * */
  static const int apiStatus_200 = 200; //success
  static const int apiStatus_201 = 201; //success
  static const int apiStatus_202 = 202; //success for static page
  static const int apiStatus_203 = 203; //success
  static const int apiStatus_205 = 205; // for remaining step 2
  static const int apiStatus_400 = 400; //Invalid data
  static const int apiStatus_401 = 401; //Invalid data
  static const int apiStatus_404 = 404; //Invalid data

  static String login = 'auth/login';
  static String register = 'auth/register';
  static String forgotPassword = 'auth/forgot-password';
  static String changePassword = 'auth/change-password';
  static String verifyOtpEmail = 'auth/verify-email';
  static String verificationMobileRequest = 'verification/mobile/request';
  static String verificationMobileVerify = 'verification/mobile/verify';
  static String adhaarVerification = 'verification/aadhaar';
  static String interestsSuggestion = 'user-interests/suggestions';

  /// Profile
  static String getProfile = 'profile';
  static String updateProfile = 'profile';
  static String createProfile = 'profile';
  static String uploadProfileMedia = 'profile/upload-media';
  static String updateLocation = 'user-location/update';
  static String deleteMedia(String mediaHashid) => 'profile/media/$mediaHashid';

  /// Recommendation
  static String getMatchRecommendations(int page, int limit, String sortBy, String order) =>
      'match/recommendations?page=$page&limit=$limit&sort_by=$sortBy&order=$order';

  /// Connection
  static String sendConnection = 'connection/send';
  static String respondConnection = 'connection/respond';
  static String destroyConnection = 'connection/destroy';
  // static String verifyPayment = 'api/connection/verify_payment';
  static String incomingConnectionRequest = 'connection/incoming-requests';
  static String outgoingConnectionRequests = 'connection/outgoing-requests';
  static String myConnectionRequests = 'connection/my-connections';

  /// Wallet
  static String addMoneyToConnection = 'wallet/add-money-to-connection';
  static String addMoneyToUserWallet = 'wallet/add-money-to-user-wallet';

  static String verifyWalletPayment(
    String razorPayOrderId,
    String razorPayPaymentId,
    String razorPaySignature,
  ) =>
      'wallet/verify-wallet-payment?razorpay_order_id=$razorPayOrderId&razorpay_payment_id=$razorPayPaymentId&razorpay_signature=$razorPaySignature';
  static String buyLynk = 'wallet/buy-lynk';

  static String verifyBuyLynk(
    String razorPayOrderId,
    String razorPayPaymentId,
    String razorPaySignature,
    int lynksPurchased,
  ) =>
      'wallet/verify-buy-lynk?razorpay_order_id=$razorPayOrderId&razorpay_payment_id=$razorPayPaymentId&razorpay_signature=$razorPaySignature&lynks_purchased=$lynksPurchased';
  static String withdraw = 'wallet/withdraw';
  static String withdrawals = 'wallet/withdrawals';
  static String getWalletBalance = 'wallet/balance';
  static String getWalletTransactions = 'wallet/transactions';
  static String requestWithdrawal = 'wallet/withdraw';

  /// MPin
  static String loginMPin = 'auth/login-mpin';
  static String forgotMPin = 'auth/forgot-mpin';
  static String setMPin = 'auth/set-mpin';
  static String verifyMPin = 'auth/verify-mpin';
  static String changeMPin = 'auth/change-mpin';
}

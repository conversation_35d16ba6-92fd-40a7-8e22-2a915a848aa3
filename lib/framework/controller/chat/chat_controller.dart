import 'dart:async';
import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/ui/utils/chat_sdk_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:dateme/framework/dependency_injection/inject.dart';

final chatController = ChangeNotifierProvider((ref) => getIt<ChatController>());

@injectable
class ChatController extends ChangeNotifier {
  /// StreamController for real-time updates
  final StreamController<List<ChatMessage>> _messageStreamController = StreamController.broadcast();

  Stream<List<ChatMessage>> get messageStream => _messageStreamController.stream;

  /// Message List
  List<ChatMessage> messages = [];

  ChatController() {
    msgListener();
  }

  void disposeController({bool isNotify = true}) {
    messages.clear();
    if (isNotify) {
      notifyListeners();
    }
  }

  void msgListener() {
    ChatSdkManager.instance.addMessageListener(
      callback: (msgList) {
        messages.addAll(msgList);
        _messageStreamController.add(messages);
        notifyListeners();
      },
    );
  }

  /// Send Message
  Future<void> sendMessage(String msg) async {
    if (msg.trim().isEmpty) return;

    await ChatSdkManager.instance.sendMessage('uctulynkusr1', msg.trim());

    // Fetch updated messages and emit to the stream
    messages = await ChatSdkManager.instance.getMessageList('uctulynkusr1');
    _messageStreamController.add(messages);
  }

  /// Get All Messages
  Future<void> getAllMessage() async {
    messages = await ChatSdkManager.instance.getMessageList('uctulynkusr1');
    _messageStreamController.add(messages);
  }

  /// Dispose StreamController
  @override
  void dispose() {
    _messageStreamController.close();
    super.dispose();
  }
}

import 'dart:io';

import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/profile_onboarding/contract/profile_repository.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/delete_media_response_model.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/update_location_response_model.dart';
import 'package:dateme/framework/utils/extension/string_extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geocoding/geocoding.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:location/location.dart' as loc;

final myProfileController = ChangeNotifierProvider((ref) => getIt<MyProfileController>());

@injectable
class MyProfileController extends ChangeNotifier {
  ProfileRepository profileRepository;

  MyProfileController(this.profileRepository);

  ///Dispose Controller
  Future<void> disposeController(BuildContext context, {bool isNotify = false}) async {
    isLoading = false;
    await getProfile(context);
    updateLocationState.isLoading = false;
    updateLocationState.success = null;

    if (isNotify) {
      notifyListeners();
    }
  }

  updateUI() {
    notifyListeners();
  }

  /// Get Profile Data
  profileData() {
    if (Session.getUserProfile() != '') {
      final responseModel = profileState.success;
      if (responseModel != null) {
        name = responseModel.fullName;
        bio = responseModel.bio;
        birthDate = responseModel.dateOfBirth;
        gender = responseModel.gender;
        occupation = responseModel.occupation;
        profileImage = responseModel.photos ?? [];
        interests = responseModel.interests ?? [];
        languages = responseModel.languages ?? [];
        height = responseModel.height ?? 170;
        lookingFor = responseModel.lookingFor;
        religion = responseModel.religion;
        maritalStatus = responseModel.maritalStatus;
        education = responseModel.education;
        company = responseModel.occupationDetails;
        incomeRange = responseModel.maxIncome ?? 'Not specified';
        drinking = responseModel.drinkingHabit;
        smoking = responseModel.smokingHabit;
        diet = responseModel.dietaryPreference;
      }
    }
    notifyListeners();
  }

  /// Already Uploaded Images
  List<PhotoModel> profileImage = [];
  String? imageError;

  /// New Selected Images
  List<File> selectedImages = [];
  String? currentAddress;
  String? errorMessage;
  bool isUpdatingLocation = false;
  double? latitude;
  double? longitude;

  // Add this method to update location
  Future<void> updateCurrentLocation(BuildContext context, WidgetRef ref) async {
    try {
      updateLoadingStatus(true);
      isUpdatingLocation = true;
      notifyListeners();

      if (await _handleLocationPermission()) {
        final loc.Location location = loc.Location();
        final loc.LocationData locationData = await location.getLocation();

        latitude = locationData.latitude;
        longitude = locationData.longitude;

        // Get address from coordinates
        List<Placemark> placemarks = await placemarkFromCoordinates(latitude!, longitude!);

        if (placemarks.isNotEmpty) {
          Placemark place = placemarks[0];
          currentAddress = '${place.locality}, ${place.administrativeArea}';
          Map<String, dynamic> map = {'current_city': place.locality, 'country': place.country};
          final profileWatch = ref.read(profileOnboardingController);
          await profileWatch.updateProfile(context, map);
        }

        await updateLocationAPI(context, latitude, longitude, locationData.accuracy ?? 0.0);

        updateLoadingStatus(false);
      }
    } catch (e) {
      errorMessage = 'Failed to get location: $e';
      commonToaster(errorMessage!);
    } finally {
      isUpdatingLocation = false;
      notifyListeners();
    }
  }

  /// Handles location permission checks and requests
  Future<bool> _handleLocationPermission() async {
    try {
      final location = loc.Location();
      bool serviceEnabled;
      loc.PermissionStatus permission;

      // Check if location services are enabled
      serviceEnabled = await location.serviceEnabled();
      if (!serviceEnabled) {
        // Request to turn on location services
        serviceEnabled = await location.requestService();
        if (!serviceEnabled) {
          errorMessage = 'Location services are disabled. Please enable the services';
          commonToaster(errorMessage!);
          notifyListeners();
          return false;
        }
      }

      // Check location permission
      permission = await location.hasPermission();
      if (permission == loc.PermissionStatus.denied) {
        // Request permission
        permission = await location.requestPermission();
        if (permission == loc.PermissionStatus.denied) {
          errorMessage = 'Location permissions are denied';
          commonToaster(errorMessage!);
          notifyListeners();
          return false;
        }
      }

      // Handle permanently denied permissions
      if (permission == loc.PermissionStatus.deniedForever) {
        errorMessage = 'Location permissions are permanently denied. Please enable in Settings';
        commonToaster(errorMessage!);
        notifyListeners();
        return false;
      }

      // Additional check for restricted permissions on iOS
      if (permission == loc.PermissionStatus.denied) {
        errorMessage = 'Location permissions are denied. Please check device settings';
        commonToaster(errorMessage!);
        notifyListeners();
        return false;
      }

      // Enable background mode if needed
      await location.enableBackgroundMode(enable: false);

      return permission == loc.PermissionStatus.granted || permission == loc.PermissionStatus.grantedLimited;
    } catch (e) {
      errorMessage = 'Error checking location permission: $e';
      commonToaster(errorMessage!);
      notifyListeners();
      return false;
    }
  }

  /// User's basic profile information
  String? name;
  String? bio;
  DateTime? birthDate;
  String? gender;
  String? occupation;
  final List<String> occupationOptions = [
    'Student',
    'Employed',
    'Business',
    'Freelancer',
    'Unemployed',
    'Other',
  ];
  bool isEditingProfile = false;

  /// Updates user's name
  Future<void> updateName(BuildContext context, WidgetRef ref, String newName) async {
    try {
      name = newName;
      updateLoadingStatus(true);

      Map<String, dynamic> map = {'full_name': newName};
      final profileWatch = ref.read(profileOnboardingController);
      await profileWatch.updateProfile(context, map);
      updateLoadingStatus(false);
      notifyListeners();
    } catch (e) {
      updateLoadingStatus(false);
      errorMessage = 'Failed to update name: $e';
      commonToaster(errorMessage!);
    }
    updateLoadingStatus(false);
  }

  /// Updates user's bio
  Future<void> updateBio(BuildContext context, WidgetRef ref, String newBio) async {
    try {
      updateLoadingStatus(true);
      bio = newBio;
      Map<String, dynamic> map = {'bio': newBio};
      final profileWatch = ref.read(profileOnboardingController);
      await profileWatch.updateProfile(context, map);
      updateLoadingStatus(false);
      notifyListeners();
    } catch (e) {
      updateLoadingStatus(false);
      errorMessage = 'Failed to update bio: $e';
      commonToaster(errorMessage!);
    }
    updateLoadingStatus(false);
  }

  /// Updates user's birth date
  Future<void> updateBirthDate(
    BuildContext context,
    WidgetRef ref,
    DateTime newDate, {
    bool isUpdate = true,
  }) async {
    try {
      // API call implementation here
      birthDate = newDate;
      if (isUpdate) {
        updateLoadingStatus(true);

        Map<String, dynamic> map = {
          'date_of_birth': birthDate.toString().getCustomDateTimeFromUTC('yyyy-MM-dd'),
        };
        final profileWatch = ref.read(profileOnboardingController);
        await profileWatch.updateProfile(context, map);
        updateLoadingStatus(false);
      }

      notifyListeners();
    } catch (e) {
      errorMessage = 'Failed to update birth date: $e';
      commonToaster(errorMessage!);
    }
  }

  /// Updates user's gender
  Future<void> updateGender(BuildContext context, WidgetRef ref, String newGender) async {
    try {
      // API call implementation here
      gender = newGender;
      updateLoadingStatus(true);

      Map<String, dynamic> map = {'gender': strToEnum(gender?.toLowerCase() ?? '')};
      final profileWatch = ref.read(profileOnboardingController);
      await profileWatch.updateProfile(context, map);
      updateLoadingStatus(false);
      notifyListeners();
    } catch (e) {
      errorMessage = 'Failed to update gender: $e';
      commonToaster(errorMessage!);
    }
  }

  /// Updates user's occupation
  Future<void> updateOccupation(BuildContext context, WidgetRef ref, String newOccupation) async {
    try {
      // API call implementation here
      occupation = newOccupation;
      updateLoadingStatus(true);

      Map<String, dynamic> map = {'occupation': strToEnum(occupation?.toLowerCase() ?? '')};
      final profileWatch = ref.read(profileOnboardingController);
      await profileWatch.updateProfile(context, map);
      updateLoadingStatus(false);
      notifyListeners();
    } catch (e) {
      errorMessage = 'Failed to update occupation: $e';
      commonToaster(errorMessage!);
    }
  }

  /// More Details Tab
  /// Interests section
  List<String> interests = [];
  List<String> interestOptions = [
    'Music',
    'Sports',
    'Travel',
    'Reading',
    'Cooking',
    'Photography',
    'Gaming',
    'Art',
    'Dancing',
    'Movies',
  ];

  /// Basic info
  List<String> languages = [];
  int height = 170;

  /// Preferences
  String? lookingFor;
  RangeValues ageRange = const RangeValues(18, 50);
  double maxDistance = 50;

  /// Education & Work
  String? education;
  String? company;
  String? incomeRange;

  /// Lifestyle
  String? smoking;
  String? drinking;
  String? diet;

  /// Personal
  String? religion;

  /// Add to existing variables
  String? maritalStatus;

  /// Add these getter/setters if needed
  String get getMaritalStatus => maritalStatus ?? 'Not specified';

  bool get hasMaritalStatus => maritalStatus != null;

  /// Update marital status and notify listeners
  void updateMaritalStatus(String value) {
    maritalStatus = value.toLowerCase();
    notifyListeners();
  }

  /// Options lists
  final List<String> relationshipOptions = [
    'Marriage',
    'Serious relationship',
    'Casual Dating',
    'Friendship',
  ];

  final List<String> educationLevels = ['High school', 'Bachelors', 'Masters', 'Phd', 'Other'];

  final List<String> incomeRanges = [
    'Below \$20,000',
    '\$20,000 - \$50,000',
    '\$50,000 - \$100,000',
    'Above \$100,000',
    'Prefer not to say',
  ];

  final List<String> smokingOptions = ['Non Smoker', 'Occasional', 'Regular', 'Quit'];

  final List<String> drinkingOptions = ['Non Drinker', 'Occasional', 'Regular', 'Quit'];

  final List<String> dietOptions = ['Vegetarian', 'Vegan', 'Non Vegetarian', 'Eggetarian', 'other'];

  /// Update methods
  void updateInterests(List<String> newInterests) {
    interests = newInterests;
    notifyListeners();
  }

  void updateLanguages(List<String> value) {
    languages = value;
    notifyListeners();
  }

  void updateHeight(int value) {
    height = value;
    notifyListeners();
  }

  void updateLookingFor(String value) {
    lookingFor = value;
    notifyListeners();
  }

  void updateAgeRange(RangeValues value) {
    ageRange = value;
    notifyListeners();
  }

  void updateMaxDistance(double value) {
    maxDistance = value;
    notifyListeners();
  }

  void updateEducation(String value) {
    education = value;
    notifyListeners();
  }

  void updateCompany(String value) {
    company = value;
    notifyListeners();
  }

  void updateIncomeRange(String value) {
    incomeRange = value;
    notifyListeners();
  }

  void updateSmoking(String value) {
    smoking = value;
    notifyListeners();
  }

  void updateDrinking(String value) {
    drinking = value;
    notifyListeners();
  }

  void updateDiet(String value) {
    diet = value;
    notifyListeners();
  }

  void updateReligion(String value) {
    religion = value;
    notifyListeners();
  }

  /// Adds images with validation for maximum and minimum count
  Future<void> addImages() async {
    try {
      final totalImages = selectedImages.length + profileImage.length;
      if (totalImages >= maxPickImageCount) {
        imageError = 'You can only add up to $maxPickImageCount photos';
        notifyListeners();
        return;
      }

      final picker = ImagePicker();
      final remainingCount = maxPickImageCount - totalImages;

      final pickedFiles = await picker.pickMultiImage();

      if (pickedFiles.isNotEmpty) {
        /// Only add up to the remaining allowed count
        final filesToAdd = pickedFiles.take(remainingCount).map((e) => File(e.path)).toList();

        selectedImages.addAll(filesToAdd);

        if (totalImages < 3) {
          imageError = 'Please upload at least 3 photos';
        } else if (pickedFiles.length > remainingCount) {
          imageError = 'Only $remainingCount ${remainingCount == 1 ? "photo" : "photos"} can be added';
        } else {
          imageError = null;
        }

        notifyListeners();
      } else if (totalImages < 3) {
        imageError = 'Please upload at least 3 photos';
        notifyListeners();
      }
    } catch (e) {
      imageError = 'Error picking images';
      notifyListeners();
    }
  }

  /// Removes image at index
  void removeImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
      notifyListeners();
    }
  }

  /// Removes Profile Image at index
  Future<void> removeProfileImage(BuildContext context, String mediaHashID, int index) async {
    await deleteMediaApi(context, mediaHashID);
    notifyListeners();
    if (deleteMediaApiState.success?.status == true) {
      if (index >= 0 && index < profileImage.length) {
        profileImage.removeAt(index);
        notifyListeners();
      }
    }
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  ///Progress Indicator
  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  /// Get Profile API
  UIState<ProfileResponse> profileState = UIState<ProfileResponse>();

  Future<void> getProfile(BuildContext context) async {
    profileState.isLoading = true;
    profileState.success = null;
    notifyListeners();

    if (context.mounted) {
      final res = await profileRepository.getProfile();
      res.when(
        success: (data) async {
          profileState.success = data;
          profileState.isLoading = false;
          profileData();
          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          profileState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    profileState.isLoading = false;
    notifyListeners();
  }

  /// Update Profile API
  UIState<UpdateLocationResponseModel> updateLocationState = UIState<UpdateLocationResponseModel>();

  Future<void> updateLocationAPI(
    BuildContext context,
    double? latitude,
    double? longitude,
    double accuracy,
  ) async {
    updateLocationState.isLoading = true;
    updateLocationState.success = null;
    notifyListeners();

    Map<String, dynamic> map = {'latitude': latitude, 'longitude': longitude, 'accuracy': accuracy.toInt()};

    if (context.mounted) {
      final res = await profileRepository.updateLocationAPI(map);
      res.when(
        success: (data) async {
          updateLocationState.success = data;
          updateLocationState.isLoading = false;

          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          updateLocationState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    updateLocationState.isLoading = false;
    notifyListeners();
  }

  /// Delete Media Api
  final deleteMediaApiState = UIState<DeleteMediaResponseModel>();

  Future<void> deleteMediaApi(BuildContext context, String mediaHashID) async {
    deleteMediaApiState.isLoading = true;
    deleteMediaApiState.success = null;
    notifyListeners();

    if (context.mounted) {
      final res = await profileRepository.deleteMediaAPI(mediaHashID);
      res.when(
        success: (data) async {
          deleteMediaApiState.success = data;
          deleteMediaApiState.isLoading = false;

          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          deleteMediaApiState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    deleteMediaApiState.isLoading = false;
    notifyListeners();
  }

  /// Set Profile Picture
  final setProfilePictureState = UIState<ProfileResponse>();

  Future<void> setProfilePicture(BuildContext context, String mediaHashID) async {
    setProfilePictureState.isLoading = true;
    setProfilePictureState.success = null;
    notifyListeners();

    if (context.mounted) {
      final res = await profileRepository.setProfilePicture(mediaHashID);
      res.when(
        success: (data) async {
          setProfilePictureState.success = data;
          setProfilePictureState.isLoading = false;

          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          setProfilePictureState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    setProfilePictureState.isLoading = false;
    notifyListeners();
  }
}

import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/api_result.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/auth/contract/auth_repository.dart';
import 'package:dateme/framework/repository/auth/model/base_error_response.dart';
import 'package:dateme/framework/repository/auth/model/common_message_response.dart';
import 'package:dateme/main.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/app_toast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final forgotPasswordController = ChangeNotifierProvider((ref) => getIt<ForgotPasswordController>());

@injectable
class ForgotPasswordController extends ChangeNotifier {
  AuthRepository repository;

  ForgotPasswordController(this.repository);

  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final otpController = List.generate(6, (index) => TextEditingController());
  final passwordController = TextEditingController();
  bool isOtpSent = false;
  String? emailError;
  String? passwordError;

  bool isLoading = false;
  bool isPasswordVisible = false;

  void togglePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    notifyListeners();
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  void disposeController() {
    emailController.clear();
    passwordController.clear();
    for (var controller in otpController) {
      controller.clear();
    }
  }

  void toggleOtpView() {
    isOtpSent = !isOtpSent;
    notifyListeners();
  }

  String? validateEmail(String? value) {
    if ((value ?? '').isEmpty) {
      emailError = 'Please enter your email';
      return emailError;
    } else if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value ?? '')) {
      emailError = 'Please enter a valid email address';
      return emailError;
    }
    emailError = null;
    // notifyListeners();
    return null;
  }

  String? validatePassword(String? value) {
    if ((value ?? '').isEmpty) {
      passwordError = 'Please enter your password';
      return passwordError;
    }
    if ((value ?? '').length < 6) {
      passwordError = 'Password must be at least 6 characters';
      return passwordError;
    }
    if (!RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$',
    ).hasMatch(value ?? '')) {
      passwordError =
          'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character';
      return passwordError;
    }
    passwordError = null;
    return passwordError;
  }

  @override
  void dispose() {
    emailController.dispose();
    for (var controller in otpController) {
      controller.dispose();
    }
    super.dispose();
  }

  String getOtp() {
    String otp = '';
    for (var controller in otpController) {
      otp += controller.text;
    }
    return otp;
  }

  Future<void> sendOtp() async {
    updateLoadingStatus(true);
    String email = emailController.text.trim();
    ApiResult apiResult = await repository?.forgotPassword(email: email);
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);
        // Handle successful login response
        CommonMessageResponse responseModel = data as CommonMessageResponse;
        if (responseModel.success ?? false) {
          if (!isOtpSent) {
            toggleOtpView();
          }
        } else {
          AppToast.showSnackBar(
            responseModel.message ?? '',
            iconImage: Icons.error,
            iconColor: AppColors.redF94008,
            textColor: AppColors.redF94008,
            decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
            snackbarKey.currentState,
            () {
              snackbarKey.currentState?.hideCurrentSnackBar();
            },
          );
        }
        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        String errorMsg = NetworkExceptions.getErrorMessage(error);
        BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
        AppToast.showSnackBar(
          resp.errorMessage,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
          () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      },
    );
  }

  //Change password
  Future<void> changePassword(WidgetRef ref) async {
    String otp = getOtp();
    if (otp.length != 6) {
      AppToast.showSnackBar(
        'Please enter a valid OTP',
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
      return;
    }
    updateLoadingStatus(true);
    String email = emailController.text.trim();
    String password = passwordController.text.trim();

    ApiResult apiResult = await repository?.changePassword(email: email, newPassword: password, otp: otp);
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);
        // Handle successful login response
        CommonMessageResponse responseModel = data as CommonMessageResponse;
        if (responseModel.success ?? false) {
          if (isOtpSent) {
            toggleOtpView();
            disposeController();
          }
          ref.read(navigationStackController).pop();
          AppToast.showSnackBar(
            responseModel.message ?? '',
            iconImage: Icons.check_circle,
            iconColor: AppColors.primary,
            textColor: AppColors.primary,
            decorationColor: AppColors.primary.withValues(alpha: 0.35),
            snackbarKey.currentState,
            () {
              snackbarKey.currentState?.hideCurrentSnackBar();
            },
          );
        } else {
          AppToast.showSnackBar(
            responseModel.message ?? '',
            iconImage: Icons.error,
            iconColor: AppColors.redF94008,
            textColor: AppColors.redF94008,
            decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
            snackbarKey.currentState,
            () {
              snackbarKey.currentState?.hideCurrentSnackBar();
            },
          );
        }
        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        String errorMsg = NetworkExceptions.getErrorMessage(error);
        BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
        AppToast.showSnackBar(
          resp.errorMessage,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
          () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      },
    );
  }
}

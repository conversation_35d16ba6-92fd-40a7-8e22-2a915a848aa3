import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/api_result.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/auth/contract/auth_repository.dart';
import 'package:dateme/framework/repository/auth/model/base_error_response.dart';
import 'package:dateme/framework/repository/auth/model/common_message_response.dart';
import 'package:dateme/framework/repository/auth/model/login_response.dart';
import 'package:dateme/framework/repository/auth/model/register_response.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/main.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/app_toast.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import 'package:intl/intl.dart';

final signupController = ChangeNotifierProvider((ref) => getIt<SignupController>());

@injectable
class SignupController extends ChangeNotifier {
  AuthRepository repository;

  SignupController(this.repository);

  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  // final confirmPasswordController = TextEditingController();
  final dobController = TextEditingController();
  String? selectedGender;

  String? nameError;
  String? emailError;
  String? passwordError;

  bool isPasswordVisible = false;
  bool isConfirmPasswordVisible = false;

  final otpFormKey = GlobalKey<FormState>();
  final otpController = List.generate(emailOtpLength, (index) => TextEditingController());

  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  String getOtp() {
    return otpController.map((controller) => controller.text).join();
  }

  void togglePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    notifyListeners();
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible = !isConfirmPasswordVisible;
    notifyListeners();
  }

  void updateGender(String? gender) {
    selectedGender = gender;
    notifyListeners();
  }

  Future<void> selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 5475)), // 15 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now().subtract(const Duration(days: 5475)),
    );
    if (picked != null) {
      dobController.text = DateFormat('yyyy-MM-dd').format(picked);
      // notifyListeners();
    }
  }

  String? validateName(String? value) {
    if ((value ?? '').isEmpty) {
      nameError = 'Please enter your name';
      // notifyListeners();
      return nameError;
    }
    nameError = null;
    // notifyListeners();
    return nameError;
  }

  String? validateEmail(String? value) {
    if ((value ?? '').isEmpty) {
      emailError = 'Please enter your email';
      // notifyListeners();
      return emailError;
    }
    if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value ?? '')) {
      emailError = 'Please enter a valid email address';
      // notifyListeners();
      return emailError;
    }
    emailError = null;
    // notifyListeners();
    return emailError;
  }

  String? validatePassword(String? value) {
    if ((value ?? '').isEmpty) {
      passwordError = 'Please enter your password';
      // notifyListeners();
      return passwordError;
    }
    if ((value ?? '').length < 6) {
      passwordError = 'Password must be at least 6 characters';
      // notifyListeners();
      return passwordError;
    }
    if (!RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$',
    ).hasMatch(value ?? '')) {
      passwordError =
          'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character';
      // notifyListeners();
      return passwordError;
    }
    passwordError = null;
    // notifyListeners();
    return null;
  }

  String? validateDob(String? value) {
    if ((value ?? '').isEmpty) {
      return 'Please select your date of birth';
    }
    return null;
  }

  String? validateGender(String? value) {
    if (value == null) {
      return 'Please select your gender';
    }
    return null;
  }

  //Api Call Signup
  Future<void> signup(WidgetRef ref) async {
    if (formKey.currentState!.validate()) {
      updateLoadingStatus(true);
      String email = emailController.text.trim().toLowerCase();
      String password = passwordController.text.trim();
      String name = nameController.text.trim();
      String dateOfBirth = dobController.text.trim();
      String gender = (selectedGender ?? '').toLowerCase();

      ApiResult apiResult = await repository.signup(
        email: email,
        password: password,
        name: name,
        dateOfBirth: dateOfBirth,
        gender: gender,
      );
      apiResult.when(
        success: (data) {
          updateLoadingStatus(false);
          RegisterResponse responseModel = data as RegisterResponse;
          // Call login API after successful signup to get token
          Session.saveLocalData(keyUserId, responseModel.userHashid);
          // Session.saveLocalData(keyUserEmail, responseModel.email);
          login(ref);
          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          updateLoadingStatus(false);
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
          AppToast.showSnackBar(
            resp.errorMessage,
            iconImage: Icons.error,
            iconColor: AppColors.redF94008,
            textColor: AppColors.redF94008,
            decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
            snackbarKey.currentState,
            () {
              snackbarKey.currentState?.hideCurrentSnackBar();
            },
          );
        },
      );
    }
  }

  UIState<CommonMessageResponse> verifyOTPState = UIState<CommonMessageResponse>();

  //Api Call Verify OTP
  Future<void> verifyOtp(WidgetRef ref, BuildContext context) async {
    String otp = getOtp();
    if (otp.length != emailOtpLength) {
      AppToast.showSnackBar(
        'Please enter a valid OTP',
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
      return;
    }

    updateLoadingStatus(true);
    ApiResult apiResult = await repository.verifyOtp(otp: otp);
    apiResult.when(
      success: (data) async {
        updateLoadingStatus(false);
        verifyOTPState.success = data as CommonMessageResponse;
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        String errorMsg = NetworkExceptions.getErrorMessage(error);
        BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
        AppToast.showSnackBar(
          resp.errorMessage,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
          () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      },
    );
  }

  //Api Call Login
  Future<void> login(WidgetRef ref) async {
    updateLoadingStatus(true);
    String email = emailController.text.trim();
    String password = passwordController.text.trim();
    ApiResult apiResult = await repository.login(email: email, password: password);
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);
        // Handle successful login response
        LoginResponse responseModel = data as LoginResponse;
        Session.saveLocalData(keyUserEmail, email);
        Session.saveLocalData(keyUserAuthToken, responseModel.accessToken);
        Session.saveLocalData(keyUserAuthTokenType, responseModel.tokenType);
        // getUserProfile(ref);
        ref.read(navigationStackController).push(NavigationStackItem.verifyOtpScreen());

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        String errorMsg = NetworkExceptions.getErrorMessage(error);
        BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
        AppToast.showSnackBar(
          resp.errorMessage,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
          () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      },
    );
  }

  // getUser Profile
  Future<void> getUserProfile(WidgetRef ref) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getProfile();
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);
        ProfileResponse responseModel = data as ProfileResponse;
        showLog('responseModel.isEmailVerified ${responseModel.isEmailVerified}');

        Session.saveLocalData(keyUserProfile, profileResponseToJson(responseModel));
        Session.saveLocalData(keyIsProfileComplete, (responseModel.profileCompletion ?? 0) != 0);
        Session.saveLocalData(keyIsEmailVerified, responseModel.isEmailVerified ?? false);

        if (!(responseModel.isEmailVerified ?? false)) {
          // if (false) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.verifyOtpScreen());
        } else if ((responseModel.profileCompletion ?? 0) != 0) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.home());
        } else {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.locationPermission());
        }
        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        // String errorMsg = NetworkExceptions.getErrorMessage(error);
        // Handle error
        ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.locationPermission());
      },
    );
  }
}

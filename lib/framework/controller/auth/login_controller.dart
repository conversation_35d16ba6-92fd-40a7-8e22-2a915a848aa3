import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/framework/repository/auth/contract/auth_repository.dart';
import 'package:dateme/framework/repository/auth/model/base_error_response.dart';
import 'package:dateme/framework/repository/auth/model/login_response.dart';
import 'package:dateme/framework/repository/common_response/model/common_response_model.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/main.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/app_toast.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final loginController = ChangeNotifierProvider((ref) => getIt<LoginController>());

@injectable
class LoginController extends ChangeNotifier {
  AuthRepository repository;

  LoginController(this.repository);

  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool isPasswordVisible = false;

  String? emailError;
  String? passwordError;

  bool isLoading = false;

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  void togglePasswordVisibility() {
    isPasswordVisible = !isPasswordVisible;
    notifyListeners();
  }

  String? validateEmail(String? value) {
    if ((value ?? '').isEmpty) {
      emailError = 'Please enter your email';
      return emailError;
    } else if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value ?? '')) {
      emailError = 'Please enter a valid email address';
      return emailError;
    } else {
      emailError = null;
      return emailError;
    }
  }

  String? validatePassword(String? value) {
    if ((value ?? '').isEmpty) {
      passwordError = 'Please enter your password';
      return passwordError;
    } else {
      passwordError = null;
      return passwordError;
    }
  }

  // Login Api call
  Future<void> login(BuildContext context, WidgetRef ref) async {
    updateLoadingStatus(true);
    String email = emailController.text.trim();
    String password = passwordController.text.trim();
    ApiResult apiResult = await repository.login(email: email, password: password);
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);
        // Handle successful login response
        LoginResponse responseModel = data as LoginResponse;
        Session.saveLocalData(keyUserAuthToken, responseModel.accessToken);
        Session.saveLocalData(keyUserAuthTokenType, responseModel.tokenType);
        Session.saveLocalData(keyUserEmail, email);
        getUserProfile(context, ref);
        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        String errorMsg = NetworkExceptions.getErrorMessage(error);
        BaseErrorResponse resp = baseErrorResponseFromJson(errorMsg);
        AppToast.showSnackBar(
          resp.errorMessage,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
          () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      },
    );
  }

  // getUser Profile
  Future<void> getUserProfile(BuildContext context, WidgetRef ref, {bool isFromUploadPhoto = false}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getProfile();
    apiResult.when(
      success: (data) {
        updateLoadingStatus(false);

        /// Handle successful login response
        ProfileResponse responseModel = data as ProfileResponse;
        Session.saveLocalData(keyUserProfile, profileResponseToJson(responseModel));
        Session.saveLocalData(keyIsProfileComplete, (responseModel.profileCompletion ?? 0) != 0);
        Session.saveLocalData(keyIsEmailVerified, responseModel.isEmailVerified ?? false);

        if (!(responseModel.isEmailVerified ?? false)) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.verifyOtpScreen());
        } else if ((responseModel.profileCompletion ?? 0) != 0) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.home());
        } else if (responseModel.country == null || responseModel.currentCity == null) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.locationPermission());
        } else if (!(responseModel.isMobileVerified ?? false)) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.mobileVerification());
        }
        /// TODO: Uncomment this when Aadhaar verification is implemented
        else if (((responseModel.photos ?? []).isEmpty) /*|| (responseModel.isKycVerified == false)*/ ) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.identityVerification());
        } else if (responseModel.maritalStatus == null ||
            responseModel.relationshipIntent == null ||
            (responseModel.photos?.length ?? 0) <= 1) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.basicProfile());
        } else if (responseModel.bio == null ||
            responseModel.interests == null ||
            responseModel.languages == null ||
            responseModel.height == null ||
            responseModel.dietaryPreference == null ||
            responseModel.smokingHabit == null ||
            responseModel.drinkingHabit == null ||
            responseModel.education == null ||
            responseModel.occupation == null) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.moreAboutYou());
        } else if (responseModel.minAge == null ||
            responseModel.maxAge == null ||
            responseModel.preferredRadius == null ||
            responseModel.lifestylePreference == null) {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.partnerPreference());
        } else if (responseModel.privacyPolicyAccepted != true) {
          ref
              .read(navigationStackController)
              .pushAndRemoveAll(NavigationStackItem.policyConfirmation(cmsUrl: privacyUrl));
        } else {
          ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.home());
        }
        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        updateLoadingStatus(false);
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg $errorMsg');
            showMessageDialog(context, 'Profile not found.', () {
              ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.signup());
            });
          },
        );

      },
    );
  }
}

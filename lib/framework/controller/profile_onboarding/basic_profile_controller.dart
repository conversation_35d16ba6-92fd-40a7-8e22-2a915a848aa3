import 'dart:io';

import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/framework/utils/extension/string_extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';

final basicProfileController = ChangeNotifierProvider((ref) => getIt<BasicProfileController>());

// lib/framework/controller/profile_onboarding/basic_profile_controller.dart

@injectable
class BasicProfileController extends ChangeNotifier {
  /// Form key for validation
  final formKey = GlobalKey<FormState>();

  /// Text controllers
  final nameController = TextEditingController();

  /// Selected values
  DateTime? selectedDate;
  String? selectedMaritalStatus;
  String? selectedIntention;
  String selectedGender = '';
  String selectedOrientation = '';
  ProfileResponse? profileResponse;

  /// Loading state
  bool isLoading = false;

  /// Error states
  String? nameError;
  String? dobError;
  String? genderError;
  String? orientationError;

  // Validation Methods
  String? validateDateOfBirth() {
    if (selectedDate == null) {
      return 'Please select your date of birth';
    }
    return null;
  }

  String? validateGender() {
    if (selectedGender == null || selectedGender.isEmpty) {
      return 'Please select your gender';
    }
    return null;
  }


  void updateGender(String gender) {
    selectedGender = gender;
    genderError = null;
    notifyListeners();
  }

  /// Gender options
  final List<String> genderOptions = ['Male', 'Female', 'Other'];

  /// Sexual orientation options
  final List<String> orientationOptions = ['Straight', 'Gay', 'Lesbian', 'Bisexual', 'Other'];

  /// Updates loading status
  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  /// Validates name
  String? validateName(String? value) {
    if ((value ?? '').isEmpty) {
      nameError = 'Please enter your name';
      return nameError;
    }
    nameError = null;
    return null;
  }

  /// Updates selected orientation
  void updateOrientation(String value) {
    selectedOrientation = value;
    notifyListeners();
  }

  /// Updates date of birth
  void updateDateOfBirth(DateTime date) {
    final now = DateTime.now();
    final age =
        now.year -
        date.year -
        (now.month > date.month || (now.month == date.month && now.day >= date.day) ? 0 : 1);

    if (age < 18) {
      dobError = 'You must be at least 18 years old';
      notifyListeners();
      return;
    }

    selectedDate = date;
    dobError = null;
    notifyListeners();
  }

  void getUserProfile() {
    if (Session.getUserProfile() != '') {
      profileResponse = profileResponseFromJson(Session.getUserProfile());

      if (profileResponse?.fullName != null) {
        nameController.text = profileResponse?.fullName ?? '';
      }

      if (profileResponse?.dateOfBirth != null) {
        selectedDate = profileResponse?.dateOfBirth;
      }

      if (profileResponse?.gender != null) {
        selectedGender = profileResponse?.gender ?? '';
      }
    }
    notifyListeners();
  }

  /// Update Marital Status
  updateMaritalStatus(String value) {
    selectedMaritalStatus = value;
    notifyListeners();
  }

  /// Validates Marital Status
  validateMaritalStatus() {
    if (selectedMaritalStatus == null || selectedMaritalStatus!.isEmpty) {
      return 'Please select your marital status';
    } else {
      return null;
    }
  }

  /// Update Intention
  updateIntentionStatus(String value) {
    selectedIntention = value;
    notifyListeners();
  }

  /// Validates Intention
  validateIntentionStatus() {
    if (selectedIntention == null || selectedIntention!.isEmpty) {
      return 'Please select your intention.';
    } else {
      return null;
    }
  }

  /// Saves profile data
  Future<void> saveProfile(WidgetRef ref, BuildContext context) async {
    if (formKey.currentState?.validate() ?? false) {
      dobError = validateDateOfBirth();
      genderError = validateGender();

      notifyListeners();
      if (dobError == null && genderError == null) {
        try {
          updateLoadingStatus(true);

          Map<String, dynamic> map = {
            'full_name': nameController.text,
            'date_of_birth': selectedDate.toString().getCustomDateTimeFromUTC('yyyy-MM-dd'),
            'gender': strToEnum(selectedGender.toLowerCase() ?? ''),
            'marital_status': strToEnum(selectedMaritalStatus?.toLowerCase() ?? ''),
            'relationship_intent': strToEnum(selectedIntention?.toLowerCase() ?? ''),
          };
          final profileWatch = ref.read(profileOnboardingController);
          await profileWatch.updateProfile(context, map);

          await Future.delayed(Duration(milliseconds: 100), () async {
            if (profileWatch.updateProfileState.success != null) {
              // if (selectedImages.isNotEmpty) {
              //   List<String> images = selectedImages.map((e) => e.path).toList();
              //
              //   await profileWatch.uploadProfileMedia(context, images, ref);
              // }
              await Future.delayed(Duration(milliseconds: 100), () {
                if (profileWatch.uploadProfileMediaState.success != null) {
                  ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.photoUpload());
                }
              });
            }
          });

          updateLoadingStatus(false);
        } catch (e) {
          updateLoadingStatus(false);
          // Handle error
        }
      }
    } else {
      dobError = validateDateOfBirth();
      genderError = validateGender();
      notifyListeners();
    }
  }

  /// Update Widget
  updateWidget() {
    notifyListeners();
  }

  /// Disposes controller
  void disposeController({bool isNotify = false}) {
    nameController.clear();
    selectedDate = null;
    selectedGender = '';
    selectedOrientation = '';
    nameError = null;
    dobError = null;
    genderError = null;
    orientationError = null;
    isLoading = false;
    profileResponse = null;
    if (isNotify) notifyListeners();
  }

  @override
  void dispose() {
    nameController.dispose();
    super.dispose();
  }
}

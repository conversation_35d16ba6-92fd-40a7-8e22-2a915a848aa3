import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../dependency_injection/inject.dart';

final partnerPreferenceController = ChangeNotifierProvider((ref) => getIt<PartnerPreferenceController>());

@injectable
class PartnerPreferenceController extends ChangeNotifier {
  double minAge = 18;
  double maxAge = 50;
  double radius = 10; // Default radius in KM
  List<String> selectedLifestyleChoices = [];
  String? otherLifestyleChoice;

  final List<String> lifestyleChoices = ['City', 'Country', 'Small Town', 'Suburban', 'Rural'];

  void updateAgeRange(double min, double max) {
    minAge = min;
    maxAge = max;
    notifyListeners();
  }

  void updateRadius(double value) {
    radius = value;
    notifyListeners();
  }

  void toggleLifestyleChoice(String choice) {
    if (selectedLifestyleChoices.contains(choice)) {
      selectedLifestyleChoices.remove(choice);
    } else {
      selectedLifestyleChoices.add(choice);
    }

    notifyListeners();
  }

  void disposeController({bool isNotify = false}) {
    minAge = 18;
    maxAge = 50;
    radius = 10;
    selectedLifestyleChoices.clear();
    otherLifestyleChoice = null;

    if (isNotify) {
      notifyListeners();
    }
  }

  bool isLoading = false;

  /// Updates loading status
  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  /// Saves profile data
  Future<void> saveProfile(WidgetRef ref, BuildContext context) async {
    try {
      updateLoadingStatus(true);
      List<String> lifeStyleList = [...selectedLifestyleChoices.map((e) => strToEnum(e.toLowerCase()))];

      Map<String, dynamic> map = {
        'min_age': minAge.toInt(),
        'max_age': maxAge.toInt(),
        'preferred_radius': radius.toInt(),
        'lifestyle_preference': lifeStyleList,
      };

      final profileWatch = ref.read(profileOnboardingController);
      await profileWatch.updateProfile(context, map);

      await Future.delayed(Duration(milliseconds: 100), () async {
        if (profileWatch.updateProfileState.success != null) {
          ref
              .read(navigationStackController)
              .push(NavigationStackItem.policyConfirmation(cmsUrl: privacyUrl));
        } else {
          showLog('Profile update failed');
        }
      });

      updateLoadingStatus(false);
    } catch (e) {
      updateLoadingStatus(false);
      // Handle error
    }
  }
}

import 'dart:io';

import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';

final photoUploadController = ChangeNotifierProvider((ref) => getIt<PhotoUploadController>());

@injectable
class PhotoUploadController extends ChangeNotifier {
  /// Form key for validation
  final formKey = GlobalKey<FormState>();

  /// Selected values
  List<File> selectedImages = [];
  ProfileResponse? profileResponse;

  /// Loading state
  bool isLoading = false;

  updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  /// Error states
  String? imageError;

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  String? validateProfilePhotos() {
    if (selectedImages.length < 3) {
      return 'Please upload at least 3 photos';
    } else if (selectedImages.length > maxPickImageCount) {
      return 'You can only add up to $maxPickImageCount photos';
    }
    return null;
  }

  updateWidget(){
    notifyListeners();
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  /// Adds images with validation for maximum and minimum count
  Future<void> addImages() async {
    try {
      if (selectedImages.length >= maxPickImageCount) {
        imageError = 'You can only add up to $maxPickImageCount photos';
        notifyListeners();
        return;
      }

      final picker = ImagePicker();
      final remainingCount = maxPickImageCount - selectedImages.length;

      final pickedFiles = await picker.pickMultiImage();

      if (pickedFiles.isNotEmpty) {
        /// Only add up to the remaining allowed count
        final filesToAdd = pickedFiles.take(remainingCount).map((e) => File(e.path)).toList();

        selectedImages.addAll(filesToAdd);

        if (selectedImages.length < 3) {
          imageError = 'Please upload at least 3 photos';
        } else if (pickedFiles.length > remainingCount) {
          imageError = 'Only $remainingCount ${remainingCount == 1 ? "photo" : "photos"} can be added';
        } else {
          imageError = null;
        }

        notifyListeners();
      } else if (selectedImages.length < 3) {
        imageError = 'Please upload at least 3 photos';
        notifyListeners();
      }
    } catch (e) {
      imageError = 'Error picking images';
      notifyListeners();
    }
  }

  /// Removes image at index
  void removeImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
      notifyListeners();
    }
  }

  /// Saves profile data
  Future<void> saveProfile(WidgetRef ref, BuildContext context) async {
    if (formKey.currentState?.validate() ?? false) {
      imageError = validateProfilePhotos();

      notifyListeners();
      if (imageError == null) {
        try {
          updateLoadingStatus(true);

          final profileWatch = ref.read(profileOnboardingController);

          await Future.delayed(Duration(milliseconds: 100), () async {
              if (selectedImages.isNotEmpty) {
                List<String> images = selectedImages.map((e) => e.path).toList();

                await profileWatch.uploadProfileMedia(context, images, ref);
              }
              await Future.delayed(Duration(milliseconds: 100), () {
                if (profileWatch.uploadProfileMediaState.success != null) {
                  ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.moreAboutYou());
                }
              });
          });

          updateLoadingStatus(false);
        } catch (e) {
          updateLoadingStatus(false);
          // Handle error
        }
      }
    } else {
      imageError = validateProfilePhotos();
      notifyListeners();
    }
  }
}

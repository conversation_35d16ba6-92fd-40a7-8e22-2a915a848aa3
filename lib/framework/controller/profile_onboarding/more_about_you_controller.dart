import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/profile_onboarding/contract/profile_repository.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final moreAboutYourController = ChangeNotifierProvider((ref) => getIt<MoreAboutYouController>());

@injectable
class MoreAboutYouController extends ChangeNotifier {
  ProfileRepository profileRepository;

  MoreAboutYouController(this.profileRepository);

  final formKey = GlobalKey<FormState>();

  // Fields
  int selectedHeight = 170;
  String? selectedDiet;
  String? selectedSmoking;
  String? selectedDrinking;
  String? selectedReligion;
  String? selectedCaste;
  String? selectedFitnessLevel;
  String? selectedEducation;
  String? selectedIncomeRange;
  String? workLocation;
  int selectedHeightFeet = 5;
  int selectedHeightInches = 0;
  String? selectedOccupation;
  List<String> selectedInterests = [];

  // Add these to MoreAboutYouController
  List<String> selectedLanguages = [];
  TextEditingController otherLanguageController = TextEditingController();
  bool showOtherLanguageField = false;

  final TextEditingController otherInterestController = TextEditingController();
  final TextEditingController bioController = TextEditingController();

  // final TextEditingController occupationController = TextEditingController();
  final TextEditingController companyController = TextEditingController();

  // Options

  final List<String> dietOptions = ['Vegetarian', 'Vegan', 'Non vegetarian', 'Eggetarian', 'Other'];
  final List<String> smokingOptions = ['Non Smoker', 'Occasional', 'Regular', 'Quit'];
  final List<String> drinkingOptions = ['Non Drinker', 'Occasional', 'Regular', 'Quit'];
  final List<String> religionOptions = ['Hindu', 'Muslim', 'Christian', 'Other'];
  final List<String> casteOptions = ['General', 'OBC', 'SC/ST', 'Other'];
  final List<String> fitnessOptions = ['Sedentary', 'Light', 'Moderate', 'Active', 'Very active'];
  final List<String> languageOptions = ['English', 'Spanish', 'French', 'German', /*'Other'*/];
  final List<String> interestOptions = ['Music', 'Sports', 'Travel', 'Reading', 'Cooking', 'Other'];
  final List<String> occupationOptions = [
    'Student',
    'Employed',
    'Business',
    'Freelancer',
    'Unemployed',
    'Other',
  ];
  final List<String> educationLevels = ['High school', 'Bachelors', 'Masters', 'Phd', 'Other'];
  final List<String> incomeRanges = [
    'Below \$20,000',
    '\$20,000 - \$50,000',
    '\$50,000 - \$100,000',
    'Above \$100,000',
    'Prefer not to say',
  ];

  // Validation
  String? validateBio(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a short bio';
    }
    return null;
  }

  String? validateEducation(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select your education level';
    }
    return null;
  }

  String? validateOccupation(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your occupation';
    }
    return null;
  }

  String? validateWorkLocation(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your work location';
    }
    return null;
  }

  void updateHeightFeet(int feet) {
    selectedHeightFeet = feet;
    notifyListeners();
  }

  void updateHeightInches(int inches) {
    selectedHeightInches = inches;
    notifyListeners();
  }

  String getHeightInCm() {
    final totalInches = (selectedHeightFeet * 12) + selectedHeightInches;
    final heightInCm = (totalInches * 2.54).toStringAsFixed(1);
    return '$heightInCm cm';
  }

  void updateDiet(String? value) {
    selectedDiet = value;
    notifyListeners();
  }

  void updateSmoking(String? value) {
    selectedSmoking = value;
    notifyListeners();
  }

  void updateDrinking(String? value) {
    selectedDrinking = value;
    notifyListeners();
  }

  void addLanguage(String language) {
    if (!selectedLanguages.contains(language)) {
      selectedLanguages.add(language);
      if (language != 'Other') {
        showOtherLanguageField = false;
      } else {
        showOtherLanguageField = true;
      }
      notifyListeners();
    }
  }

  void removeLanguage(String language) {
    selectedLanguages.remove(language);
    if (language == 'Other') {
      showOtherLanguageField = false;
      otherLanguageController.clear();
    }
    notifyListeners();
  }

  void addCustomLanguage(String language) {
    if (language.isNotEmpty && !selectedLanguages.contains(language)) {
      selectedLanguages.add(language);
      otherLanguageController.clear();
      notifyListeners();
    }
  }

  // Update Methods
  void addInterest(String interest) {
    if (!selectedInterests.contains(interest)) {
      selectedInterests.add(interest);
      notifyListeners();
    }
  }

  void removeInterest(String interest) {
    selectedInterests.remove(interest);
    if (interest == 'Other') {
      otherInterestController.clear();
    }
    notifyListeners();
  }

  void updateReligion(String? value) {
    selectedReligion = value;
    notifyListeners();
  }

  void updateCaste(String? value) {
    selectedCaste = value;
    notifyListeners();
  }

  void updateFitnessLevel(String? value) {
    selectedFitnessLevel = value;
    notifyListeners();
  }

  void updateEducation(String? value) {
    selectedEducation = value;
    notifyListeners();
  }

  void updateOccupation(String? value) {
    selectedOccupation = value;
    notifyListeners();
  }

  void updateIncomeRange(String? value) {
    selectedIncomeRange = value;
    notifyListeners();
  }

  void disposeController() {
    interestListApiState.success = null;
  }

  /// Api Call For Interests
  UIState<List<String>> interestListApiState = UIState<List<String>>();

  Future<void> interestListApi(BuildContext context) async {
    interestListApiState.isLoading = true;
    interestListApiState.success = null;
    notifyListeners();

    if (context.mounted) {
      final res = await profileRepository.interestsSuggestionApi();
      res.when(
        success: (data) async {
          interestListApiState.success = data;
          interestListApiState.isLoading = false;
          interestOptions.clear();
          interestOptions.addAll(interestListApiState.success ?? []);
          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          interestListApiState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    interestListApiState.isLoading = false;
    notifyListeners();
  }
}

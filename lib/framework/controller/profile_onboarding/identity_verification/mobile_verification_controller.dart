import 'dart:async';

import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/profile_onboarding/contract/profile_repository.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/send_otp_response_model.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/verify_mobile_otp_response_model.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final mobileVerificationController = ChangeNotifierProvider(
  (ref) => getIt<MobileVerificationController>(),
);

@injectable
class MobileVerificationController extends ChangeNotifier {
  ProfileRepository profileRepository;

  MobileVerificationController(this.profileRepository);

  final otpController = List.generate(mobileOtpLength, (index) => TextEditingController());
  final formKey = GlobalKey<FormState>();
  final formKey1 = GlobalKey<FormState>();
  final phoneController = TextEditingController();
  bool isPhoneVerified = false;
  String? phoneError;
  String? otpError;
  bool isLoading = false;
  bool isResendEnabled = false;
  int resendTimer = 60;

  /// Validates phone number format
  String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      phoneError = 'Please enter phone number';
      // notifyListeners();
      return phoneError;
    }
    if (value.length != 10) {
      phoneError = 'Please enter valid phone number';
      // notifyListeners();
      return phoneError;
    }
    phoneError = null;
    // notifyListeners();
    return null;
  }

  /// Validates OTP
  String? validateOTP(String? value) {
    if (otpController.any((controller) => controller.text.isEmpty)) {
      otpError = LocaleKeys.keyPleaseEnterOTP;
      return otpError;
    } else {
      for (var controller in otpController) {
        if (controller.text.isEmpty) {
          otpError = LocaleKeys.keyPleaseEnterOTP;
          return otpError;
        }
      }
    }

    otpError = null;
    return null;
  }

  /// Update Widget
  void updateWidget() {
    notifyListeners();
  }

  String getOtp() {
    return otpController.map((controller) => controller.text).join();
  }

  /// Starts resend timer
  void startResendTimer() {
    isResendEnabled = false;
    resendTimer = 60;
    notifyListeners();

    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (resendTimer == 0) {
        timer.cancel();
        isResendEnabled = true;
        notifyListeners();
      } else {
        resendTimer--;
        notifyListeners();
      }
    });
  }

  /// Updates loading status
  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  /// Disposes controller
  void disposeController({bool isNotify = false}) {
    phoneController.clear();
    isPhoneVerified = false;
    phoneError = null;
    otpError = null;
    isLoading = false;
    isResendEnabled = false;
    resendTimer = 60;
    if (isNotify) notifyListeners();
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  /// ------------------------------ API ------------------------------ ///

  UIState<SendOtpResponseModel> sendOTPState = UIState<SendOtpResponseModel>();

  Future<void> sendOTPMobile(BuildContext context) async {
    sendOTPState.isLoading = true;
    sendOTPState.success = null;
    notifyListeners();

    Map<String, dynamic> map = {'mobile_number': phoneController.text};

    if (context.mounted) {
      final res = await profileRepository.sendOTPMobile(map);
      res.when(
        success: (data) async {
          sendOTPState.success = data;
          sendOTPState.isLoading = false;

          /// TODO: remove after testing
          showMessageDialog( context, sendOTPState.success?.message ?? '', (){});
          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          sendOTPState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    sendOTPState.isLoading = false;
    notifyListeners();
  }

  UIState<VerifyMobileOtpResponseModel> verifyOTPState = UIState<VerifyMobileOtpResponseModel>();

  Future<void> verifyOTPMobile(BuildContext context) async {
    verifyOTPState.isLoading = true;
    verifyOTPState.success = null;
    notifyListeners();

    Map<String, dynamic> map = {'mobile_number': phoneController.text, 'otp': getOtp()};

    if (context.mounted) {
      final res = await profileRepository.verifyOTPMobile(map);
      res.when(
        success: (data) async {
          verifyOTPState.success = data;
          verifyOTPState.isLoading = false;

          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          verifyOTPState.isLoading = false;
          String errorMsg = NetworkExceptions.getErrorMessage(error);
          showMessageDialog(context, errorMsg, () {});
        },
      );
    }
    verifyOTPState.isLoading = false;
    notifyListeners();
  }
}

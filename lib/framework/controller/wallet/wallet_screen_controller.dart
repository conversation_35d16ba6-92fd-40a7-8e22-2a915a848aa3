import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/api_result.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/connection/model/payment_success_model.dart';
import 'package:dateme/framework/repository/wallet/contract/wallet_repository.dart';
import 'package:dateme/framework/repository/wallet/model/buy_lynk_response_model.dart';
import 'package:dateme/framework/repository/wallet/model/get_wallet_balance_response_model.dart';
import 'package:dateme/framework/repository/wallet/model/transaction_history_response.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final walletController = ChangeNotifierProvider((ref) => getIt<WalletController>());

@injectable
class WalletController extends ChangeNotifier {
  WalletRepository walletRepository;

  WalletController(this.walletRepository);

  disposeWalletController({bool isNotify = false}) {
    customAmountController.clear();
    selectedAmount = 0;
    if (isNotify) {
      notifyListeners();
    }
  }

  updateUI(){
    notifyListeners();
  }

  final TextEditingController customAmountController = TextEditingController();
  int selectedAmount = 0;
  int balance = 0; // Add this to store current balance

  List<int> predefinedAmounts = [100, 500, 1000, 2500, 5000];

  // List<TransactionHistoryResponse>? transactionList;

  void updateCustomAmount(String value) {
    if (value.isNotEmpty) {
      selectedAmount = int.tryParse(value) ?? 0;
    } else {
      selectedAmount = 0;
    }
    notifyListeners();
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    buyLynkState.isLoading = false;
    buyLynkState.success = null;
    verifyBuyLynkState.isLoading = false;
    verifyBuyLynkState.success = null;
    getWalletBalanceState.isLoading = false;
    getWalletBalanceState.success = null;
    if (isNotify) {
      notifyListeners();
    }
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  /// Buy Lynk API
  UIState<BuyLynkResponseModel> buyLynkState = UIState<BuyLynkResponseModel>();

  Future<void> buyLynkAPI() async {
    buyLynkState.isLoading = true;
    buyLynkState.success = null;
    Map<String, dynamic> map = {'amount': customAmountController.text, 'lynks': customAmountController.text};

    ApiResult apiResult = await walletRepository.buyLynkAPI(map);
    apiResult.when(
      success: (data) {
        buyLynkState.isLoading = false;
        buyLynkState.success = data;

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        buyLynkState.isLoading = false;
        commonToaster('Failed to buy lynks. Please try again.');
      },
    );
    buyLynkState.isLoading = false;
    notifyListeners();
  }

  /// Verify Buy Lynk API
  UIState<BuyLynkResponseModel> verifyBuyLynkState = UIState<BuyLynkResponseModel>();

  Future<void> verifyPaymentAPI(
    WidgetRef ref,
    RazorPayPaymentSuccessModel paymentResponse,
    int lynksPurchased,
  ) async {
    verifyBuyLynkState.isLoading = true;
    verifyBuyLynkState.success = null;

    ApiResult apiResult = await walletRepository.verifyBuyLynkAPI(
      paymentResponse.orderId,
      paymentResponse.paymentId,
      paymentResponse.orderSignature,
      lynksPurchased,
    );
    apiResult.when(
      success: (data) {
        verifyBuyLynkState.isLoading = false;
        verifyBuyLynkState.success = data;

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        verifyBuyLynkState.isLoading = false;
      },
    );
    verifyBuyLynkState.isLoading = false;
    notifyListeners();
  }

  /// Get Wallet Balance API
  UIState<GetWalletBalanceResponseModel> getWalletBalanceState = UIState<GetWalletBalanceResponseModel>();

  Future<void> getWalletBalanceAPI() async {
    getWalletBalanceState.isLoading = true;
    getWalletBalanceState.success = null;

    ApiResult apiResult = await walletRepository.getWalletBalanceAPI();
    apiResult.when(
      success: (data) {
        getWalletBalanceState.isLoading = false;
        getWalletBalanceState.success = data;

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        getWalletBalanceState.isLoading = false;
        commonToaster('Failed to buy lynks. Please try again.');
      },
    );
    getWalletBalanceState.isLoading = false;
    notifyListeners();
  }

  UIState<List<TransactionHistoryResponse>> transactionHistoryState =
      UIState<List<TransactionHistoryResponse>>();

  /// Get Transaction History API
  Future<void> getTransactionHistory() async {
    transactionHistoryState.isLoading = true;
    try {
      ApiResult apiResult = await walletRepository.getTransactionHistory();
      apiResult.when(
        success: (data) {
          transactionHistoryState.isLoading = false;

          // transactionList = data as List<TransactionHistoryResponse>;
          transactionHistoryState.success = data as List<TransactionHistoryResponse>;
          notifyListeners();
        },
        failure: (NetworkExceptions error) {
          transactionHistoryState.success = [];
          transactionHistoryState.isLoading = false;
          notifyListeners();
        },
      );
    } catch (e) {
      transactionHistoryState.success = [];
      transactionHistoryState.isLoading = false;
    }
    notifyListeners();
  }
}

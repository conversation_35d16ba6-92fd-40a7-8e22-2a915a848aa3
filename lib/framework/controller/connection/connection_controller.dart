import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/network/api_result.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/framework/repository/common_response/model/common_response_model.dart';
import 'package:dateme/framework/repository/connection/contract/connection_repository.dart';
import 'package:dateme/framework/repository/connection/model/incoming_request_response_model.dart';
import 'package:dateme/framework/repository/connection/model/payment_success_model.dart';
import 'package:dateme/framework/repository/connection/model/respond_connection_response_model.dart';
import 'package:dateme/framework/repository/connection/model/send_connection_response_model.dart';
import 'package:dateme/framework/repository/connection/model/verify_payment_response_model.dart';
import 'package:dateme/framework/repository/recommendation/model/get_match_recommendation_list_response_model.dart';
import 'package:dateme/framework/utils/ui_state.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final connectionController = ChangeNotifierProvider((ref) => getIt<ConnectionController>());

@injectable
class ConnectionController extends ChangeNotifier {
  ConnectionRepository connectionRepository;

  ConnectionController(this.connectionRepository);

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    sendConnectionState.isLoading = false;
    sendConnectionState.success = null;
    destroyConnectionState.isLoading = false;
    destroyConnectionState.success = null;
    incomingRequestState.isLoading = false;
    incomingRequestState.success = null;
    incomingRequest.clear();
    outgoingRequestState.isLoading = false;
    outgoingRequestState.success = null;
    outgoingRequest.clear();
    myConnectionState.isLoading = false;
    myConnectionState.success = null;
    myConnectionsList.clear();
    if (isNotify) {
      notifyListeners();
    }
  }

  /*
  /// ---------------------------- Api Integration ---------------------------------///
   */

  /// Send Connection API
  UIState<SendConnectionResponseModel> sendConnectionState = UIState<SendConnectionResponseModel>();

  Future<void> sendConnectionAPI(String userHashId) async {
    sendConnectionState.isLoading = true;
    sendConnectionState.success = null;
    Map<String, dynamic> map = {
      'receiver_hashid': userHashId,
      'message': '',

      /// TODo: Add message if needed
    };

    ApiResult apiResult = await connectionRepository.sendConnectionAPI(map);
    apiResult.when(
      success: (data) {
        sendConnectionState.isLoading = false;
        sendConnectionState.success = data;

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        sendConnectionState.isLoading = false;
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg errorMsg $errorMsg');
          },
          paymentRequired: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            sendConnectionState.isPaymentRequired = true;
            sendConnectionState.message = errorMsg;
          },
        );
        showLog('errorMsg $errorMsg');
        // commonToaster(errorMsg, second: 4);
      },
    );
    sendConnectionState.isLoading = false;
    notifyListeners();
  }

  /// Destroy Connection API (Dislike)
  UIState<GetMatchRecommendationListResponseModel> destroyConnectionState =
      UIState<GetMatchRecommendationListResponseModel>();

  /// Incoming Request API
  UIState<IncomingRequestResponseModel> incomingRequestState = UIState<IncomingRequestResponseModel>();
  List<Connection> incomingRequest = [];

  Future<void> incomingConnectionRequestAPI() async {
    incomingRequestState.isLoading = true;
    incomingRequestState.success = null;
    incomingRequest.clear();

    ApiResult apiResult = await connectionRepository.incomingConnectionRequestAPI();
    apiResult.when(
      success: (data) {
        incomingRequestState.isLoading = false;
        incomingRequestState.success = data;

        if (incomingRequestState.success?.connections?.isNotEmpty ?? false) {
          incomingRequest.addAll(incomingRequestState.success?.connections ?? []);
        }

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        incomingRequestState.isLoading = false;
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg errorMsg $errorMsg');
          },
          paymentRequired: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            incomingRequestState.isPaymentRequired = true;
            incomingRequestState.message = errorMsg;
          },
        );
        showLog('errorMsg $errorMsg');
        // commonToaster(errorMsg, second: 4);
      },
    );
    incomingRequestState.isLoading = false;
    notifyListeners();
  }

  /// Outgoing Request API
  UIState<IncomingRequestResponseModel> outgoingRequestState = UIState<IncomingRequestResponseModel>();
  List<Connection> outgoingRequest = [];

  Future<void> outgoingConnectionRequestsAPI() async {
    outgoingRequestState.isLoading = true;
    outgoingRequestState.success = null;
    outgoingRequest.clear();

    ApiResult apiResult = await connectionRepository.outgoingConnectionRequestsAPI();
    apiResult.when(
      success: (data) {
        outgoingRequestState.isLoading = false;
        outgoingRequestState.success = data;

        if (outgoingRequestState.success?.connections?.isNotEmpty ?? false) {
          outgoingRequest.addAll(outgoingRequestState.success?.connections ?? []);
        }

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        outgoingRequestState.isLoading = false;
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg errorMsg $errorMsg');
          },
          paymentRequired: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            outgoingRequestState.isPaymentRequired = true;
            outgoingRequestState.message = errorMsg;
          },
        );
        showLog('errorMsg $errorMsg');
        // commonToaster(errorMsg, second: 4);
      },
    );
    outgoingRequestState.isLoading = false;
    notifyListeners();
  }

  /// My Connections API
  UIState<IncomingRequestResponseModel> myConnectionState = UIState<IncomingRequestResponseModel>();
  List<Connection> myConnectionsList = [];

  Future<void> myConnectionRequestsAPI() async {
    myConnectionState.isLoading = true;
    myConnectionState.success = null;
    myConnectionsList.clear();

    ApiResult apiResult = await connectionRepository.myConnectionRequestsAPI();
    apiResult.when(
      success: (data) {
        myConnectionState.isLoading = false;
        myConnectionState.success = data;

        if (myConnectionState.success?.connections?.isNotEmpty ?? false) {
          myConnectionsList.addAll(myConnectionState.success?.connections ?? []);
        }

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        myConnectionState.isLoading = false;
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg errorMsg $errorMsg');
          },
          paymentRequired: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            myConnectionState.isPaymentRequired = true;
            myConnectionState.message = errorMsg;
          },
        );
        showLog('errorMsg $errorMsg');
        // commonToaster(errorMsg, second: 4);
      },
    );
    myConnectionState.isLoading = false;
    notifyListeners();
  }

  /// Respond Connection Api
  final respondApiState = UIState<RespondConnectionResponseModel>();

  Future<void> respondConnectionApi(String userId, String status) async {
    respondApiState.isLoading = true;
    respondApiState.success = null;

    ApiResult apiResult = await connectionRepository.respondConnectionAPI({
      'connection_hashid': userId,
      'response': status,
      'message_to_sender': '',
      'message_to_platform': '',
    });
    apiResult.when(
      success: (data) {
        respondApiState.isLoading = false;
        respondApiState.success = data;

        notifyListeners();
      },
      failure: (NetworkExceptions error) {
        respondApiState.isLoading = false;
        showLog('error $error');
        String errorMsg = error.toString();
        error.whenOrNull(
          notFound: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            showLog('errorMsg errorMsg $errorMsg');
          },
          paymentRequired: (reason, response) {
            CommonResponseModel commonResponseModel = commonResponseModelFromJson(response.toString());
            errorMsg = commonResponseModel.detail ?? '';
            respondApiState.message = errorMsg;
          },
        );
        showLog('errorMsg $errorMsg');
      },
    );
    respondApiState.isLoading = false;
    notifyListeners();
  }
}

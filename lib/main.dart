import 'package:dateme/firebase_manager/firebase_push_notification_manager.dart';
import 'package:dateme/firebase_options.dart';
import 'package:dateme/framework/controller/dark_mode/theme_mode_controller.dart';
import 'package:dateme/framework/dependency_injection/inject.dart';
import 'package:dateme/framework/provider/local_storage/hive/hive_provider.dart';
import 'package:dateme/framework/provider/local_storage/local_const.dart';
import 'package:dateme/framework/provider/network/network_exceptions.dart';
import 'package:dateme/ui/routing/delegate.dart';
import 'package:dateme/ui/routing/parser.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/chat_sdk_manager.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/theme/theme_style.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

final snackbarKey = GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> globalNavigatorKey = GlobalKey<NavigatorState>();

/// To verify things are working, check out the native platform logs.
@pragma('vm:entry-point')
Future firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  showLog('Handling a background message ${message.toMap().toString()}');
  FirebaseMessaging.onMessageOpenedApp.listen(firebaseOnMessageOpenedAppHandler);
}

Future<void> firebaseOnMessageOpenedAppHandler(RemoteMessage? message) async {
  WidgetsFlutterBinding.ensureInitialized();

  SchedulerBinding.instance.addPostFrameCallback((_) async {
    // Map<String, dynamic> messageData = message?.data ?? {};

    showLog('_onReceiveNotification Called++++++++++');
    showLog('Push Data - $message');
  });
}

Future<void> initializeFirebase() async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
}

Future<void> main() async {
  await HiveProvider.init();

  WidgetsFlutterBinding.ensureInitialized();

  await ChatSdkManager.instance.initializeAgoraChat();

  /// Environment
  await configureMainDependencies(environment: Env.dev);

  /// Hive
  await Hive.initFlutter();
  await Hive.openBox(LocalConst.appBox);

  /// Firebase Initialization
  await initializeFirebase();

  /// Background Handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  /// Theme For Status Bar & Navigation Bar
  systemTheme();

  /// Crash Analytics
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  runApp(ProviderScope(child: const MyApp()));
}

class MyApp extends ConsumerStatefulWidget {
  /// Global NavigatorKey
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {

  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      /// Firebase Messaging--------------------------
      await FirebasePushNotificationManager.instance.setupInteractedMessage(context, ref);
    });
  }

  /// dispose method
  @override
  void dispose() {
    Hive.box(LocalConst.appBox).compact();
    Hive.close();
    super.dispose();
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    globalContext = context;
    globalReference = ref;
    var themeModeController = ref.watch(themeModeProvider);

    /// Theme For Status Bar & Navigation Bar
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        /// The color of top status bar.
        ///
        /// Only honored in Android version M and greater.
        statusBarColor: Colors.transparent,

        /// The brightness of the top status bar icons.
        ///
        /// Only honored in Android version M and greater.
        statusBarIconBrightness: AppColors.isDarkMode ? Brightness.light : Brightness.dark,

        /// The brightness of top status bar.
        ///
        /// Only honored in iOS.
        statusBarBrightness: AppColors.isDarkMode ? Brightness.dark : Brightness.light,

        /// The color of the system bottom navigation bar.
        ///
        /// Only honored in Android versions O and greater.
        systemNavigationBarColor: AppColors.isDarkMode ? AppColors.white : AppColors.black,

        /// The brightness of the system navigation bar icons.
        ///
        /// Only honored in Android versions O and greater.
        /// When set to [Brightness.light], the system navigation bar icons are light.
        /// When set to [Brightness.dark], the system navigation bar icons are dark.
        systemNavigationBarIconBrightness: AppColors.isDarkMode ? Brightness.light : Brightness.dark,
      ),
    );

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: appName,
      themeMode: themeModeController.themeMode,
      theme: ThemeStyle.themeData(false, context),
      scaffoldMessengerKey: snackbarKey,
      backButtonDispatcher: RootBackButtonDispatcher(),
      routerDelegate: getIt<MainRouterDelegate>(param1: ref.read(navigationStackController)),
      routeInformationParser: getIt<MainRouterInformationParser>(param1: ref, param2: context),
    );
  }
}

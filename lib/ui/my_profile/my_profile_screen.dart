import 'package:dateme/framework/controller/my_profile/my_profile_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/ui/my_profile/helper/about_tab.dart';
import 'package:dateme/ui/my_profile/helper/location_tab.dart';
import 'package:dateme/ui/my_profile/helper/more_details_tab.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MyProfileScreen extends ConsumerStatefulWidget {
  const MyProfileScreen({super.key});

  @override
  ConsumerState<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends ConsumerState<MyProfileScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    SchedulerBinding.instance.addPostFrameCallback((_) {
      ref.read(myProfileController).disposeController(context, isNotify: true);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return commonBackgroundDecoration(
      context,
      child: Scaffold(
        backgroundColor: AppColors.transparent,
        appBar: CommonAppBar(
          title: 'My Profile',
          isShowBack: false,
          isCenterTitle: true,
          bottom: _buildTabBar(),
          bgColor: AppColors.transparent,
          actionWidgets: [
            InkWell(
              onTap: () => ref.read(navigationStackController).push(NavigationStackItem.settingScreen()),
              child: Icon(Icons.settings_outlined, color: AppColors.black),
            ).paddingOnly(right: 16.w),
          ],
        ),
        body: Stack(
          children: [
            _buildBody(),
            DialogProgressBar(isLoading: ref.watch(profileOnboardingController).updateProfileState.isLoading || ref.watch(myProfileController).profileState.isLoading),
          ],
        ),
      ),
    );
  }

  PreferredSize _buildTabBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(48.h),
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.primary,
        indicatorWeight: 2,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: TextStyles.medium.copyWith(fontSize: 15.sp),
        unselectedLabelStyle: TextStyles.medium.copyWith(fontSize: 15.sp),
        tabs: const [Tab(text: 'About'), Tab(text: 'More Details')],
      ),
    );
  }

  Widget _buildBody() {
    final controller = ref.watch(myProfileController);
    return TabBarView(
      controller: _tabController,
      children: [
        AboutTab(controller: controller),
        // LocationTab(controller: controller),
        MoreDetailsTab(controller: controller),
      ],
    );
  }
}

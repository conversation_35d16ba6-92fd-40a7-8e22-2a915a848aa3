import 'package:dateme/framework/controller/my_profile/my_profile_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/more_about_you_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Tab for displaying and editing detailed user profile information
class MoreDetailsTab extends ConsumerStatefulWidget {
  const MoreDetailsTab({super.key, required this.controller});

  final MyProfileController controller;

  @override
  ConsumerState<MoreDetailsTab> createState() => _MoreDetailsTabState();
}

class _MoreDetailsTabState extends ConsumerState<MoreDetailsTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildBasicInfoCard(ref),
            SizedBox(height: 20.h),
            _buildInterestsCard(),
            SizedBox(height: 20.h),
            _buildPreferencesCard(),
            SizedBox(height: 20.h),
            _buildEducationCard(),
            SizedBox(height: 20.h),
            _buildLifestyleCard(),
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }

  /// Basic info card with bio, languages and height
  Widget _buildBasicInfoCard(WidgetRef ref) {
    return _DetailCard(
      title: 'Basic Info',
      icon: Icons.person_outline,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // _EditableField(title: 'Bio', value: widget.controller.bio ?? 'Add a bio', onTap: () => _showBioDialog(context, ref)),
          // _Divider(),
          _EditableField(
            title: 'Marital Status',
            value: widget.controller.maritalStatus ?? 'Add marital status',
            onTap:
                () => showBottomSheetDialog(
                  context: context,
                  child: CommonBottomSheet(
                    title: 'Marital Status',
                    selected: widget.controller.maritalStatus,
                    items: const ['Single', 'Divorced', 'Widowed', 'Separated', 'Married'],
                    onSelect: (value) {
                      widget.controller.updateMaritalStatus(value);
                      Map<String, dynamic> map = {'marital_status': value.toLowerCase()};
                      final profileWatch = ref.read(profileOnboardingController);
                      profileWatch.updateProfile(context, map);
                    },
                  ),
                ),
          ),
          _Divider(),
          _ChipsSection(
            title: 'Languages',
            items: widget.controller.languages,
            onAdd: () => _showLanguagesDialog(context),
            onDelete: (item) {
              widget.controller.languages.remove(item);
              Map<String, dynamic> map = {'languages': widget.controller.languages};
              final profileWatch = ref.read(profileOnboardingController);
              profileWatch.updateProfile(context, map);
              widget.controller.updateUI();
            },
          ),
          _Divider(),
          _EditableField(
            title: 'Height',
            value: '${widget.controller.height} cm',
            onTap: () => _showHeightPicker(context),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Religion',
            value: widget.controller.religion ?? 'Not specified',
            onTap: () => _showReligionDialog(context),
          ),
        ],
      ),
    );
  }

  /// Interests card with chips
  Widget _buildInterestsCard() {
    return _DetailCard(
      title: 'Interests',
      icon: Icons.favorite_outline,
      child: _ChipsSection(
        items: widget.controller.interests,
        onAdd: () => _showInterestsDialog(context),
        onDelete: (item) {
          widget.controller.interests.remove(item);
          Map<String, dynamic> map = {'interests': widget.controller.interests};
          final profileWatch = ref.read(profileOnboardingController);
          profileWatch.updateProfile(context, map);
          widget.controller.updateUI();
        },
        emptyText: 'Add your interests to find better matches',
      ),
    );
  }

  /// Preferences card for relationship preferences
  Widget _buildPreferencesCard() {
    return _DetailCard(
      title: 'Preferences',
      icon: Icons.favorite_border,
      child: Column(
        children: [
          _PreferenceItem(
            title: 'Looking For',
            value: widget.controller.lookingFor ?? 'Not specified',
            onTap:
                () =>
                    _showOptionsDialog(context, 'Looking For', widget.controller.relationshipOptions, (val) {
                      widget.controller.updateLookingFor(val);
                      Map<String, dynamic> map = {'relationship_intent': strToEnum(val.toLowerCase())};
                      final profileWatch = ref.read(profileOnboardingController);
                      profileWatch.updateProfile(context, map);
                    }),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Age Range',
            value:
                '${widget.controller.ageRange.start.round()}-'
                '${widget.controller.ageRange.end.round()} years',
            onTap: () => _showAgeRangeDialog(context),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Maximum Distance',
            value: '${widget.controller.maxDistance.round()} km',
            onTap: () => _showDistanceDialog(context),
          ),
        ],
      ),
    );
  }

  /// Education and work details card
  Widget _buildEducationCard() {
    return _DetailCard(
      title: 'Education & Work',
      icon: Icons.school_outlined,
      child: Column(
        children: [
          _PreferenceItem(
            title: 'Education',
            value: widget.controller.education ?? 'Not specified',
            onTap:
                () =>
                    _showOptionsDialog(context, 'Education Level', widget.controller.educationLevels, (val) {
                      widget.controller.updateEducation(val);
                      Map<String, dynamic> map = {'education': strToEnum(val.toLowerCase())};
                      final profileWatch = ref.read(profileOnboardingController);
                      profileWatch.updateProfile(context, map);
                    }),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Occupation',
            value: widget.controller.occupation ?? 'Not specified',
            onTap:
                () => _showOptionsDialog(context, 'Occupation', widget.controller.occupationOptions, (str) {
                  widget.controller.updateOccupation(context, ref, str);
                  Map<String, dynamic> map = {'occupation': strToEnum(str.toLowerCase())};
                  final profileWatch = ref.read(profileOnboardingController);
                  profileWatch.updateProfile(context, map);
                }),
          ),
          if (widget.controller.occupation != null) ...[
            _Divider(),
            _EditableField(
              title: 'Company',
              value: widget.controller.company ?? 'Add company name',
              onTap: () => _showCompanyDialog(context),
            ),
          ],
          _Divider(),
          _PreferenceItem(
            title: 'Income Range',
            value: widget.controller.incomeRange ?? 'Not specified',
            onTap:
                () => _showOptionsDialog(context, 'Income Range', widget.controller.incomeRanges, (val) {
                  widget.controller.updateIncomeRange(val);
                  Map<String, dynamic> map = {'annual_income': val};
                  final profileWatch = ref.read(profileOnboardingController);
                  profileWatch.updateProfile(context, map);
                }),
          ),
        ],
      ),
    );
  }

  /// Lifestyle preferences card
  Widget _buildLifestyleCard() {
    return _DetailCard(
      title: 'Lifestyle',
      icon: Icons.sports_basketball,
      child: Column(
        children: [
          _PreferenceItem(
            title: 'Smoking',
            value: widget.controller.smoking ?? 'Not specified',
            onTap:
                () => _showOptionsDialog(context, 'Smoking Habits', widget.controller.smokingOptions, (val) {
                  widget.controller.updateSmoking(val);
                  Map<String, dynamic> map = {'smoking_habit': strToEnum(val.toLowerCase())};
                  final profileWatch = ref.read(profileOnboardingController);
                  profileWatch.updateProfile(context, map);
                }),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Drinking',
            value: widget.controller.drinking ?? 'Not specified',
            onTap:
                () =>
                    _showOptionsDialog(context, 'Drinking Habits', widget.controller.drinkingOptions, (val) {
                      widget.controller.updateDrinking(val);
                      Map<String, dynamic> map = {'drinking_habit': strToEnum(val.toLowerCase())};
                      final profileWatch = ref.read(profileOnboardingController);
                      profileWatch.updateProfile(context, map);
                    }),
          ),
          _Divider(),
          _PreferenceItem(
            title: 'Diet',
            value: widget.controller.diet ?? 'Not specified',
            onTap:
                () => _showOptionsDialog(context, 'Dietary Preference', widget.controller.dietOptions, (val) {
                  widget.controller.updateDiet(val);
                  Map<String, dynamic> map = {'dietary_preference': strToEnum(val.toLowerCase())};
                  final profileWatch = ref.read(profileOnboardingController);
                  profileWatch.updateProfile(context, map);
                }),
          ),
        ],
      ),
    );
  }

  /// Shows dialog for selecting languages
  void _showLanguagesDialog(BuildContext context) {
    final List<String> tempSelectedLanguages = List.from(widget.controller.languages);
    final List<String> availableLanguages = [
      'English',
      'Spanish',
      'French',
      'German',
      'Italian',
      'Portuguese',
      'Russian',
      'Chinese',
      'Japanese',
      'Korean',
      'Arabic',
      'Hindi',
      'Bengali',
      'Turkish',
      'Other',
    ];

    showWidgetDialog(
      context,
      StatefulBuilder(
        builder: (context, setState) {
          return Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.language, color: AppColors.buttonColor, size: 24.w),
                    SizedBox(width: 12.w),
                    Text(
                      'Select Languages',
                      style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children:
                      availableLanguages.map((language) {
                        final bool isSelected = tempSelectedLanguages.contains(language);
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                tempSelectedLanguages.remove(language);
                              } else
                              /*if (tempSelectedLanguages.length < 10)*/ {
                                tempSelectedLanguages.add(language);
                              }
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                            decoration: BoxDecoration(
                              color: isSelected ? AppColors.primaryLight : AppColors.white,
                              borderRadius: BorderRadius.circular(20.r),
                              border: Border.all(
                                color: isSelected ? AppColors.primary : AppColors.grey2B2B2B,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  language,
                                  style: TextStyles.medium.copyWith(
                                    fontSize: 14.sp,
                                    color: isSelected ? AppColors.primary : AppColors.textPrimary,
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                Icon(
                                  isSelected ? Icons.remove : Icons.add,
                                  size: 16.w,
                                  color: isSelected ? AppColors.primary : AppColors.grey2B2B2B,
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: _DialogButton(
                        onTap: () => Navigator.pop(context),
                        text: 'Cancel',
                        isOutlined: true,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _DialogButton(
                        onTap: () {
                          widget.controller.updateLanguages(tempSelectedLanguages);
                          Map<String, dynamic> map = {'languages': tempSelectedLanguages};
                          final profileWatch = ref.read(profileOnboardingController);
                          profileWatch.updateProfile(context, map);
                          Navigator.pop(context);
                        },
                        text: 'Save',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting height
  void _showHeightPicker(BuildContext context) {
    int selectedFeet = (widget.controller.height / 30.48).floor();
    int selectedInches = ((widget.controller.height / 2.54) % 12).round();

    showWidgetDialog(
      context,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
        child: StatefulBuilder(
          builder: (context, setState) {
            // Calculate cm
            double heightInCm = (selectedFeet * 30.48) + (selectedInches * 2.54);

            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(Icons.height, color: AppColors.buttonColor, size: 24.w),
                    SizedBox(width: 12.w),
                    Text(
                      'Select Height',
                      style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'Feet',
                            style: TextStyles.medium.copyWith(
                              fontSize: 16.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          SizedBox(
                            height: 120.h,
                            child: CupertinoPicker(
                              itemExtent: 40.h,
                              onSelectedItemChanged: (index) {
                                setState(() {
                                  selectedFeet = index + 4;
                                });
                              },
                              scrollController: FixedExtentScrollController(initialItem: selectedFeet - 4),
                              children: List.generate(
                                4,
                                (index) => Center(
                                  child: Text(
                                    '${index + 4}\'',
                                    style: TextStyles.medium.copyWith(
                                      fontSize: 16.sp,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          Text(
                            'Inches',
                            style: TextStyles.medium.copyWith(
                              fontSize: 16.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          SizedBox(
                            height: 120.h,
                            child: CupertinoPicker(
                              itemExtent: 40.h,
                              onSelectedItemChanged: (index) {
                                setState(() {
                                  selectedInches = index;
                                });
                              },
                              scrollController: FixedExtentScrollController(initialItem: selectedInches),
                              children: List.generate(
                                12,
                                (index) => Center(
                                  child: Text(
                                    '$index"',
                                    style: TextStyles.medium.copyWith(
                                      fontSize: 16.sp,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                Text(
                  '${heightInCm.round()} cm',
                  style: TextStyles.medium.copyWith(fontSize: 18.sp, color: AppColors.buttonColorDark),
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: _DialogButton(
                        onTap: () => Navigator.pop(context),
                        text: 'Cancel',
                        isOutlined: true,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _DialogButton(
                        onTap: () {
                          widget.controller.updateHeight(heightInCm.round());
                          Map<String, dynamic> map = {'height': heightInCm.round()};
                          final profileWatch = ref.read(profileOnboardingController);
                          profileWatch.updateProfile(context, map);
                          Navigator.pop(context);
                        },
                        text: 'Save',
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting interests
  void _showInterestsDialog(BuildContext context) {
    final controller = ref.watch(moreAboutYourController);
    controller.disposeController();
    controller.interestListApi(context);
    final interestOptions = controller.interestOptions;
    final List<String> tempSelectedInterests = List.from(widget.controller.interests);

    showWidgetDialog(
      context,
      StatefulBuilder(
        builder: (context, setState) {
          return Container(
            width: double.infinity,
            padding: EdgeInsets.all(24.w),
            decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.favorite, color: AppColors.buttonColor, size: 24.w),
                    SizedBox(width: 12.w),
                    Text(
                      'Select Interests',
                      style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Wrap(
                  spacing: 8.w,
                  runSpacing: 8.h,
                  children:
                      interestOptions.map((interest) {
                        final bool isSelected = tempSelectedInterests.contains(interest);
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (isSelected) {
                                tempSelectedInterests.remove(interest);
                              } else {
                                tempSelectedInterests.add(interest);
                              }
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                            decoration: BoxDecoration(
                              color: isSelected ? AppColors.primaryLight : AppColors.white,
                              borderRadius: BorderRadius.circular(20.r),
                              border: Border.all(
                                color: isSelected ? AppColors.primary : AppColors.grey2B2B2B,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  interest,
                                  style: TextStyles.medium.copyWith(
                                    fontSize: 14.sp,
                                    color: isSelected ? AppColors.primary : AppColors.textPrimary,
                                  ),
                                ),
                                SizedBox(width: 4.w),
                                Icon(
                                  isSelected ? Icons.remove : Icons.add,
                                  size: 16.w,
                                  color: isSelected ? AppColors.primary : AppColors.grey2B2B2B,
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: _DialogButton(
                        onTap: () => Navigator.pop(context),
                        text: 'Cancel',
                        isOutlined: true,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _DialogButton(
                        onTap: () {
                          widget.controller.updateInterests(tempSelectedInterests);
                          Map<String, dynamic> map = {'interests': tempSelectedInterests};
                          final profileWatch = ref.read(profileOnboardingController);
                          profileWatch.updateProfile(context, map);
                          Navigator.pop(context);
                        },
                        text: 'Save',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting age range
  void _showAgeRangeDialog(BuildContext context) {
    RangeValues selectedRange = widget.controller.ageRange;

    showWidgetDialog(
      context,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
        child: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(Icons.person_outline, color: AppColors.buttonColor, size: 24.w),
                    SizedBox(width: 12.w),
                    Text(
                      'Age Range',
                      style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Text(
                  '${selectedRange.start.round()} - ${selectedRange.end.round()} years',
                  style: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
                ),
                SizedBox(height: 16.h),
                RangeSlider(
                  values: selectedRange,
                  min: 18,
                  max: 99,
                  divisions: 81,
                  activeColor: AppColors.primary,
                  inactiveColor: AppColors.grey2B2B2B,
                  overlayColor: WidgetStateProperty.all(AppColors.primary.withAlpha(200)),
                  labels: RangeLabels(
                    selectedRange.start.round().toString(),
                    selectedRange.end.round().toString(),
                  ),
                  onChanged: (RangeValues values) {
                    setState(() {
                      selectedRange = values;
                    });
                  },
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: _DialogButton(
                        onTap: () => Navigator.pop(context),
                        text: 'Cancel',
                        isOutlined: true,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _DialogButton(
                        onTap: () {
                          widget.controller.updateAgeRange(selectedRange);
                          Map<String, dynamic> map = {
                            'min_age': selectedRange.start,
                            'max_age': selectedRange.end,
                          };
                          final profileWatch = ref.read(profileOnboardingController);
                          profileWatch.updateProfile(context, map);
                          Navigator.pop(context);
                        },
                        text: 'Save',
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting distance
  void _showDistanceDialog(BuildContext context) {
    double selectedDistance = widget.controller.maxDistance;

    showWidgetDialog(
      context,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
        child: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(Icons.place_outlined, color: AppColors.buttonColor, size: 24.w),
                    SizedBox(width: 12.w),
                    Text(
                      'Maximum Distance',
                      style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                    ),
                  ],
                ),
                SizedBox(height: 24.h),
                Text(
                  '${selectedDistance.round()} km',
                  style: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
                ),
                SizedBox(height: 16.h),
                Slider(
                  value: selectedDistance,
                  min: 1,
                  max: 100,
                  divisions: 99,
                  activeColor: AppColors.primary,
                  inactiveColor: AppColors.grey2B2B2B,
                  label: selectedDistance.round().toString(),
                  onChanged: (double value) {
                    setState(() {
                      selectedDistance = value;
                    });
                  },
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: _DialogButton(
                        onTap: () => Navigator.pop(context),
                        text: 'Cancel',
                        isOutlined: true,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: _DialogButton(
                        onTap: () {
                          widget.controller.updateMaxDistance(selectedDistance);
                          Map<String, dynamic> map = {'preferred_radius': selectedDistance};
                          final profileWatch = ref.read(profileOnboardingController);
                          profileWatch.updateProfile(context, map);
                          Navigator.pop(context);
                        },
                        text: 'Save',
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting options (generic)
  void _showOptionsDialog(BuildContext context, String title, List<String> options, Function(String) onSave) {
    showWidgetDialog(
      context,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(Icons.list, color: AppColors.buttonColor, size: 24.w),
                SizedBox(width: 12.w),
                Text(title, style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary)),
              ],
            ),
            SizedBox(height: 24.h),
            ...options.map(
              (option) => _OptionItem(
                title: option,
                onTap: () {
                  onSave(option);
                  Navigator.pop(context);
                },
              ),
            ),
            SizedBox(height: 16.h),
            _DialogButton(onTap: () => Navigator.pop(context), text: 'Cancel', isOutlined: true),
          ],
        ),
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for entering company name
  void _showCompanyDialog(BuildContext context) {
    final TextEditingController companyController = TextEditingController(text: widget.controller.company);

    showWidgetDialog(
      context,
      Container(
        width: double.infinity,
        padding: EdgeInsets.all(24.w),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.business, color: AppColors.buttonColor, size: 24.w),
                SizedBox(width: 12.w),
                Text(
                  'Company Name',
                  style: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            CommonInputFormField(
              textEditingController: companyController,
              hintText: 'Enter company name',
              validator: (val) => null,
              borderColor: AppColors.primary,
            ),
            SizedBox(height: 24.h),
            Row(
              children: [
                Expanded(
                  child: _DialogButton(onTap: () => Navigator.pop(context), text: 'Cancel', isOutlined: true),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _DialogButton(
                    onTap: () {
                      widget.controller.updateCompany(companyController.text.trim());
                      Navigator.pop(context);
                    },
                    text: 'Save',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      () {},
      isDismissDialog: true,
    );
  }

  /// Shows dialog for selecting religion
  void _showReligionDialog(BuildContext context) {
    final List<String> religions = [
      'Christianity',
      'Islam',
      'Hinduism',
      'Buddhism',
      'Judaism',
      'Sikhism',
      'Other',
      'Prefer not to say',
    ];

    _showOptionsDialog(context, 'Religion', religions, (val) {
      widget.controller.updateReligion(val);
      Map<String, dynamic> map = {'religion': val.toLowerCase()};
      final profileWatch = ref.read(profileOnboardingController);
      profileWatch.updateProfile(context, map);
    });
  }
}

class _OptionItem extends StatelessWidget {
  const _OptionItem({required this.title, required this.onTap});

  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
          child: Text(
            title,
            style: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
          ),
        ),
      ),
    );
  }
}

class _DialogButton extends StatelessWidget {
  const _DialogButton({required this.onTap, required this.text, this.isOutlined = false});

  final VoidCallback onTap;
  final String text;
  final bool isOutlined;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 14.h),
          decoration: BoxDecoration(
            color: isOutlined ? AppColors.transparent : AppColors.primary,
            borderRadius: BorderRadius.circular(12.r),
            border: isOutlined ? Border.all(color: AppColors.primary) : null,
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyles.semiBold.copyWith(
                fontSize: 16.sp,
                color: isOutlined ? AppColors.primary : AppColors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Card widget for displaying a section of details
class _DetailCard extends StatelessWidget {
  const _DetailCard({required this.title, required this.icon, required this.child});

  final String title;
  final IconData icon;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(15), blurRadius: 10, offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: AppColors.buttonColor, size: 24.w),
              SizedBox(width: 12.w),
              CommonText(
                title: title,
                textStyle: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
              ),
            ],
          ),
          SizedBox(height: 20.h),
          child,
        ],
      ),
    );
  }
}

/// Field widget for editable content
class _EditableField extends StatelessWidget {
  const _EditableField({required this.title, required this.value, required this.onTap});

  final String title;
  final String value;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonText(
              title: title,
              textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
            SizedBox(height: 4.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CommonText(
                    title: value,
                    textStyle: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
                  ),
                ),
                Icon(Icons.edit, color: AppColors.buttonColor, size: 20.w),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Divider widget for visual separation
class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1.h,
      thickness: 1.h,
      color: AppColors.dividerColor,
    ).paddingOnly(top: 10.h, bottom: 10.h);
  }
}

/// Section for displaying chips with add functionality
class _ChipsSection extends ConsumerWidget {
  const _ChipsSection({
    this.title,
    required this.items,
    required this.onAdd,
    required this.onDelete,
    this.emptyText,
  });

  final String? title;
  final List<String> items;
  final VoidCallback onAdd;
  final Function onDelete;
  final String? emptyText;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          CommonText(
            title: title!,
            textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 12.h),
        ],
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: [
            ...items.map(
              (item) => _Chip(
                label: item,
                onDelete: () {
                  onDelete(item);
                },
              ),
            ),
            _AddChip(onTap: onAdd),
          ],
        ),
        if (items.isEmpty && emptyText != null) ...[
          SizedBox(height: 8.h),
          CommonText(
            title: emptyText!,
            textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
        ],
      ],
    );
  }
}

/// Item widget for preferences
class _PreferenceItem extends StatelessWidget {
  const _PreferenceItem({required this.title, required this.value, required this.onTap});

  final String title;
  final String value;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonText(
                    title: title,
                    textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
                  ),
                  SizedBox(height: 4.h),
                  CommonText(
                    title: value,
                    textStyle: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right, color: AppColors.buttonColor, size: 24.w),
          ],
        ),
      ),
    );
  }
}

/// Chip widget for displaying selected items
class _Chip extends StatelessWidget {
  const _Chip({required this.label, required this.onDelete});

  final String label;
  final VoidCallback onDelete;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: AppColors.primaryLight,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: AppColors.primary),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonText(
            title: label,
            textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.primary),
          ),
          SizedBox(width: 4.w),
          InkWell(onTap: onDelete, child: Icon(Icons.close, size: 16.w, color: AppColors.primary)),
        ],
      ),
    );
  }
}

/// Add chip widget for adding new items
class _AddChip extends StatelessWidget {
  const _AddChip({required this.onTap});

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: AppColors.primary),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add, size: 16.w, color: AppColors.primary),
            SizedBox(width: 4.w),
            CommonText(
              title: 'Add',
              textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.primary),
            ),
          ],
        ),
      ),
    );
  }
}

// Function to show bottom sheet dialog
void showBottomSheetDialog({required BuildContext context, required Widget child}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder:
        (context) => Container(
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(20.r), topRight: Radius.circular(20.r)),
          ),
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: child,
        ),
  );
}

// Common bottom sheet widget
class CommonBottomSheet extends StatelessWidget {
  final String title;
  final List<String> items;
  final String? selected;
  final Function(String) onSelect;

  const CommonBottomSheet({
    super.key,
    required this.title,
    required this.items,
    this.selected,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(border: Border(bottom: BorderSide(color: AppColors.dividerColor))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(title, style: TextStyles.bold.copyWith(fontSize: 18.sp, color: AppColors.textPrimary)),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close, color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
          ListView.builder(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.h),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              final isSelected = selected?.toLowerCase() == item.toLowerCase();
      
              return InkWell(
                onTap: () {
                  onSelect(item);
                  Navigator.pop(context);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        item,
                        style: TextStyles.medium.copyWith(
                          fontSize: 16.sp,
                          color: isSelected ? AppColors.primary : AppColors.textPrimary,
                        ),
                      ),
                      if (isSelected) Icon(Icons.check, color: AppColors.primary, size: 20.w),
                    ],
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }
}

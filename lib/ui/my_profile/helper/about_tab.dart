import 'dart:io';

import 'package:dateme/framework/controller/my_profile/my_profile_controller.dart';
import 'package:dateme/framework/repository/profile_onboarding/model/profile_response.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/my_profile/helper/more_details_tab.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class AboutTab extends ConsumerStatefulWidget {
  const AboutTab({super.key, required this.controller});

  final MyProfileController controller;

  @override
  ConsumerState<AboutTab> createState() => _AboutTabState();
}

class _AboutTabState extends ConsumerState<AboutTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _ProfileCard(controller: widget.controller),
            SizedBox(height: 20.h),
            _InfoCard(
              title: 'About Me',
              content: widget.controller.bio ?? 'Add a bio to tell others about yourself',
              onEdit:
                  () => _showEditDialog(
                    context: context,
                    title: 'Edit Bio',
                    maxLength: maxBioLength,
                    initialValue: widget.controller.bio,
                    onSave: (str) {
                      widget.controller.updateBio(context, ref, str);
                    },
                    isMultiline: true,
                  ),
            ),
            SizedBox(height: 20.h),
            _DetailsList(controller: widget.controller),
            _buildPhotoUpload(context, widget.controller),
            SizedBox(height: 100.h),
          ],
        ),
      ),
    );
  }
}

/// Shows a modern edit dialog using common_dialog
void _showEditDialog({
  required BuildContext context,
  required String title,
  required String? initialValue,
  int? maxLength,
  required Function(String) onSave,
  bool isMultiline = false,
}) {
  final controller = TextEditingController(text: initialValue);

  showWidgetDialog(
    context,
    Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.edit, color: AppColors.buttonColor, size: 24.w),
              SizedBox(width: 12.w),
              CommonText(
                title: title,
                textStyle: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
              ),
            ],
          ),
          SizedBox(height: 24.h),
          CommonInputFormField(
            textEditingController: controller,
            maxLines: isMultiline ? 4 : 1,
            maxLength: maxLength,
            borderRadius: BorderRadius.circular(12.r),
            hintText: 'Enter ${title.toLowerCase()}',
            hintTextStyle: TextStyles.regular.copyWith(fontSize: 16.sp, color: AppColors.textSecondary),
            fieldTextStyle: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
            validator: (String? val) {
              return null;
            },
          ),
          if (controller.text.isNotEmpty)
            ValueListenableBuilder<TextEditingValue>(
              valueListenable: controller,
              builder: (context, value, child) {
                return Padding(
                  padding: EdgeInsets.only(top: 4.h, left: 8.w),
                  child: Text(
                    '${value.text.length} / $maxBioLength',
                    style: TextStyle(fontSize: 12.sp, color: AppColors.black0E0E0E),
                  ),
                );
              },
            ).alignAtBottomRight(),
          SizedBox(height: 24.h),
          Row(
            children: [
              Expanded(
                child: _DialogButton(onTap: () => Navigator.pop(context), text: 'Cancel', isOutlined: true),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _DialogButton(
                  onTap: () {
                    onSave(controller.text);
                    Navigator.pop(context);
                  },
                  text: 'Save',
                ),
              ),
            ],
          ),
        ],
      ),
    ),
    () {},
    isDismissDialog: true,
  );
}

/// Custom button for dialog actions
class _DialogButton extends StatefulWidget {
  const _DialogButton({
    required this.onTap,
    required this.text,
    this.isOutlined = false,
    this.isLoading = false,
  });

  final VoidCallback onTap;
  final String text;
  final bool isOutlined;
  final bool isLoading;

  @override
  State<_DialogButton> createState() => _DialogButtonState();
}

class _DialogButtonState extends State<_DialogButton> {
  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: widget.isOutlined ? Colors.transparent : AppColors.buttonColor,
            borderRadius: BorderRadius.circular(12.r),
            border: widget.isOutlined ? Border.all(color: AppColors.buttonColor) : null,
          ),
          alignment: Alignment.center,
          child:
              widget.isLoading
                  ? Center(child: LoadingAnimationWidget.waveDots(color: AppColors.white, size: 40.h))
                  : CommonText(
                    title: widget.text,
                    textStyle: TextStyles.semiBold.copyWith(
                      fontSize: 16.sp,
                      color: widget.isOutlined ? AppColors.buttonColor : AppColors.white,
                    ),
                  ),
        ),
      ),
    );
  }
}

class _ProfileCard extends ConsumerWidget {
  const _ProfileCard({required this.controller});

  final MyProfileController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(15), blurRadius: 10, offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          if (controller.profileImage.isNotEmpty)
            Stack(
              children: [
                Container(
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [AppColors.buttonColor.withAlpha(55), AppColors.buttonColor],
                    ),
                  ),
                  child: CircleAvatar(
                    radius: 60.r,
                    backgroundColor: AppColors.white,
                    child: CommonImage(
                      strIcon: controller.profileImage.first.mediaUrl ?? '',
                      height: 110.w,
                      width: 110.w,
                      boxFit: BoxFit.cover,
                      bottomLeftRadius: 100.r,
                      bottomRightRadius: 100.r,
                      topLeftRadius: 100.r,
                      topRightRadius: 100.r,
                    ),
                  ),
                ),
              ],
            ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CommonText(
                title: controller.name ?? 'Add Name',
                textStyle: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.textPrimary),
              ),
              SizedBox(width: 8.w),
            ],
          ),
        ],
      ),
    );
  }
}

class _InfoCard extends StatelessWidget {
  const _InfoCard({required this.title, required this.content, required this.onEdit});

  final String title;
  final String content;
  final VoidCallback onEdit;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(15), blurRadius: 10, offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CommonText(
                title: title,
                textStyle: TextStyles.semiBold.copyWith(fontSize: 18.sp, color: AppColors.textPrimary),
              ),
              const Spacer(),
              _EditButton(onTap: onEdit),
            ],
          ),
          SizedBox(height: 12.h),
          CommonText(
            title: content,
            textStyle: TextStyles.regular.copyWith(
              fontSize: 15.sp,
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

class _DetailsList extends ConsumerWidget {
  const _DetailsList({required this.controller});

  final MyProfileController controller;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(15), blurRadius: 10, offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        children: [
          _DetailItem(
            icon: Icons.cake,
            title: 'Birthday',
            value:
                controller.birthDate != null
                    ? DateFormat('MMMM d, yyyy').format(controller.birthDate!)
                    : 'Add Birthday',
            // onEdit: () => _showDatePicker(context, ref),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Divider(color: AppColors.dividerColor),
          ),
          _DetailItem(
            icon: Icons.person,
            title: 'Gender',
            value: controller.gender ?? 'Add Gender',
            // onEdit: () => _showGenderPicker(context, controller, ref),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Divider(color: AppColors.dividerColor),
          ),
          _DetailItem(
            icon: Icons.work,
            title: 'Occupation',
            value: controller.occupation ?? 'Add Occupation',
            onEdit:
                () => showBottomSheetDialog(
                  context: context,
                  child: CommonBottomSheet(
                    title: 'Select Occupation',
                    items: controller.occupationOptions,
                    selected: controller.occupation,
                    onSelect: (value) {
                      controller.updateOccupation(context, ref, value);
                    },
                  ),
                ),
          ),
        ],
      ),
    );
  }
}

class _DetailItem extends StatelessWidget {
  const _DetailItem({required this.icon, required this.title, required this.value, this.onEdit});

  final IconData icon;
  final String title;
  final String value;
  final VoidCallback? onEdit;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(color: AppColors.primaryLight, borderRadius: BorderRadius.circular(12.r)),
          child: Icon(icon, color: AppColors.buttonColor, size: 22.w),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonText(
                title: title,
                textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
              ),
              SizedBox(height: 4.h),
              CommonText(
                title: value,
                textStyle: TextStyles.semiBold.copyWith(fontSize: 16.sp, color: AppColors.textPrimary),
              ),
            ],
          ),
        ),
        onEdit != null ? _EditButton(onTap: onEdit) : const SizedBox.shrink(),
      ],
    );
  }
}

class _EditButton extends StatelessWidget {
  const _EditButton({
    this.onTap,
    this.icon = Icons.edit,
    this.size,
    this.backgroundColor,
    this.isLoading = false,
  });

  final VoidCallback? onTap;
  final IconData icon;
  final double? size;
  final Color? backgroundColor;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.primaryLight,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          if (backgroundColor != null)
            BoxShadow(color: AppColors.black.withAlpha(25), blurRadius: 4, offset: const Offset(0, 2)),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child:
            isLoading == true
                ? Center(child: LoadingAnimationWidget.waveDots(color: AppColors.white, size: 40.h))
                : InkWell(
                  borderRadius: BorderRadius.circular(20.r),
                  onTap: onTap,
                  child: Padding(
                    padding: EdgeInsets.all(8.w),
                    child: Icon(icon, color: AppColors.buttonColor, size: size ?? 20.w),
                  ),
                ),
      ),
    );
  }
}

/// Shows gender picker dialog
void _showGenderPicker(BuildContext context, MyProfileController controller, WidgetRef ref) {
  final genderOptions = ['Male', 'Female', 'Other'];

  showWidgetDialog(
    context,
    Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.person, color: AppColors.buttonColor, size: 24.w),
              SizedBox(width: 12.w),
              CommonText(
                title: 'Select Gender',
                textStyle: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
              ),
            ],
          ),
          SizedBox(height: 24.h),
          ...genderOptions.map(
            (gender) => _buildGenderOption(
              context: context,
              gender: gender,
              isSelected: controller.gender == gender,
              onTap: () {
                controller.updateGender(context, ref, gender);
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    ),
    () {},
    isDismissDialog: true,
  );
}

/// Builds individual gender option item
Widget _buildGenderOption({
  required BuildContext context,
  required String gender,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return Padding(
    padding: EdgeInsets.only(bottom: 12.h),
    child: Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primaryLight : Colors.transparent,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isSelected ? AppColors.buttonColor : AppColors.grey9F9F9F.withAlpha(75),
            ),
          ),
          child: Row(
            children: [
              CommonText(
                title: gender,
                textStyle: TextStyles.medium.copyWith(
                  fontSize: 16.sp,
                  color: isSelected ? AppColors.buttonColor : AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              if (isSelected) Icon(Icons.check_circle, color: AppColors.buttonColor, size: 20.w),
            ],
          ),
        ),
      ),
    ),
  );
}

Widget _buildPhotoUpload(BuildContext context, MyProfileController controller) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [fieldsHeading('Profile Photos')]),
      fieldsSubHeading(
        'Upload 3 or more photos. Your first photo will be your profile photo, the rest will be shown as your gallery posts.',
      ),
      GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8.w,
          mainAxisSpacing: 8.h,
        ),
        itemCount: maxPickImageCount,
        itemBuilder: (context, index) {
          // First check if we have API images
          if (controller.profileImage.isNotEmpty && index < controller.profileImage.length) {
            return _buildNetworkPhotoItem(context, controller, controller.profileImage, index);
          }
          // Then check if we have locally selected images
          final localImageIndex = index - (controller.profileImage.length);
          if (localImageIndex >= 0 && localImageIndex < controller.selectedImages.length) {
            return _buildLocalPhotoItem(
              controller,
              controller.selectedImages[localImageIndex],
              localImageIndex,
            );
          }
          // If no image for this slot, show add button
          return _buildAddPhotoButton(controller);
        },
      ),
      if (controller.imageError != null)
        Padding(
          padding: EdgeInsets.only(top: 4.h),
          child: CommonText(
            title: controller.imageError!,
            textStyle: TextStyle(color: AppColors.redF94008, fontSize: 12.sp),
          ),
        ),
    ],
  );
}

Widget _buildNetworkPhotoItem(
  BuildContext context,
  MyProfileController controller,
  List<PhotoModel> imageUrl,
  int index,
) {
  return Stack(
    children: [
      ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CommonImage(
          strIcon: imageUrl[index].mediaUrl ?? '',
          boxFit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
        ),
      ),
      Positioned(
        top: 4,
        right: 4,
        child: InkWell(
          onTap: () {
            if (imageUrl.length <= 1) {
              controller.imageError = 'You cannot delete the last photo';
              controller.updateUI();
              return;
            } else {
              showConfirmationDialog(
                context,
                'Delete Media',
                'Are you sure want to delete this media?',
                'Yes',
                'No',
                (isPositive) {
                  if (isPositive) {
                    controller.removeProfileImage(context, imageUrl[index].mediaHashid ?? '', index);
                  }
                },
              );
            }
          },
          // Add this method to controller
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [BoxShadow(color: Colors.black.withAlpha(77), blurRadius: 4)],
            ),
            child: Icon(Icons.close, size: 16.w, color: AppColors.redF94008),
          ),
        ),
      ),
    ],
  );
}

Widget _buildLocalPhotoItem(MyProfileController controller, File image, int index) {
  return Stack(
    children: [
      ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.file(image, fit: BoxFit.cover, width: double.infinity, height: double.infinity),
      ),
      Positioned(
        top: 4,
        right: 4,
        child: InkWell(
          onTap: () => controller.removeImage(index),
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              boxShadow: [BoxShadow(color: Colors.black.withAlpha(77), blurRadius: 4)],
            ),
            child: Icon(Icons.close, size: 16.w, color: AppColors.redF94008),
          ),
        ),
      ),
    ],
  );
}

Widget _buildAddPhotoButton(MyProfileController controller) {
  final totalImages = controller.profileImage.length + controller.selectedImages.length;

  return InkWell(
    onTap: () {
      if (totalImages < maxPickImageCount) {
        controller.addImages();
      } else {
        controller.imageError = 'You can only add up to $maxPickImageCount photos';
        controller.updateUI();
      }
    },
    child: Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withAlpha(77)),
      ),
      child: Icon(Icons.add_photo_alternate_outlined, color: AppColors.primary, size: 30.w),
    ),
  );
}

/// Fields Heading
Widget fieldsHeading(String value) {
  return CommonText(
    title: value,
    textStyle: TextStyles.semiBold.copyWith(fontSize: 18.sp, color: AppColors.black),
  ).paddingOnly(top: 20.h, bottom: 8.h);
}

Widget fieldsSubHeading(String value) {
  return CommonText(
    title: value,
    maxLines: 10,
    textStyle: TextStyles.semiBold.copyWith(fontSize: 15.sp, color: AppColors.black.withAlpha(150)),
  ).paddingOnly(bottom: 8.h);
}

/// Shows date picker dialog with custom styling
void _showDatePicker(BuildContext context, WidgetRef ref) {
  final controller = ref.watch(myProfileController);
  DateTime tempSelectedDate = controller.birthDate ?? DateTime.now();

  showWidgetDialog(
    context,
    Consumer(
      builder: (context, ref, child) {
        final controller = ref.watch(myProfileController);
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.cake, color: AppColors.buttonColor, size: 24.w),
                  SizedBox(width: 12.w),
                  CommonText(
                    title: 'Select Birthday',
                    textStyle: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                  ),
                ],
              ),
              SizedBox(height: 24.h),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: AppColors.buttonColor.withAlpha(55)),
                ),
                child: Column(
                  children: [
                    Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: Theme.of(context).colorScheme.copyWith(
                          primary: AppColors.buttonColor, // Selected date background
                          onPrimary: AppColors.buttonColorDark, // Selected date text
                          surface: AppColors.primaryLight, // Today's date background
                          onSurface: AppColors.textPrimary, // Regular dates
                        ),
                      ),
                      child: CalendarDatePicker(
                        initialDate: tempSelectedDate,
                        firstDate: DateTime(1900),

                        lastDate: DateTime(DateTime.now().year - 18),
                        currentDate: tempSelectedDate,
                        onDateChanged: (date) {
                          tempSelectedDate = date;
                          controller.updateUI();
                        },
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: CommonText(
                        title: 'Selected: ${DateFormat('MMMM d, yyyy').format(tempSelectedDate)}',
                        textStyle: TextStyles.medium.copyWith(
                          fontSize: 16.sp,
                          color: AppColors.buttonColorDark,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: _DialogButton(
                      onTap: () => Navigator.pop(context),
                      text: 'Cancel',
                      isOutlined: true,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: _DialogButton(
                      onTap: () {
                        controller.updateBirthDate(context, ref, tempSelectedDate);
                        Navigator.pop(context);
                      },
                      text: 'Save',
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    ),
    () {},
    isDismissDialog: true,
  );
}

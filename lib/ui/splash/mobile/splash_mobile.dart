import 'package:dateme/framework/controller/auth/login_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/device_configuration.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';

class SplashMobile extends ConsumerStatefulWidget {
  const SplashMobile({super.key});

  @override
  ConsumerState<SplashMobile> createState() => _SplashMobileState();
}

class _SplashMobileState extends ConsumerState<SplashMobile> with SingleTickerProviderStateMixin{
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(seconds: 2));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: const Interval(0.0, 0.5, curve: Curves.easeIn)),
    );

    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: const Interval(0.3, 0.8, curve: Curves.easeOut)),
    );

    _animationController.forward();
    AnalyticsEvents.appOpened.track();
    Future.delayed(const Duration(seconds: 4), () {
      if (Session.isMPinSet()) {
        ref
            .read(navigationStackController)
            .pushAndRemoveAll(NavigationStackItem.mPinScreen(isFromSplash: true));
      } else {
        if (Session.getUserAccessToken().isNotEmpty && !Session.getIsEmailVerified()) {
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.login());
        } else if (Session.getUserAccessToken().isNotEmpty && Session.getIsProfileComplete()) {
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.home());
        } else if (Session.getUserAccessToken().isNotEmpty && !Session.getIsProfileComplete()) {
          ref.read(loginController).getUserProfile(context, ref);
          // ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.identityVerification());
        } else {
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.intro());
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    mobileDeviceConfiguration(context);
    return Scaffold(
      body: commonBackgroundDecoration(
        context,
        child: Stack(
          children: [
            /// Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Lottie.asset(
                    Assets.animation.appLogo,
                    repeat: false,
                    height: 200.h,
                  ).paddingOnly(bottom: 10.h),
                  // CommonImage(
                  //   height: 150.h,
                  //   width: 150.h,
                  //   boxFit: BoxFit.contain,
                  //   strIcon: Assets.svgs.svgAppIcon,
                  // ),
                  // SizedBox(height: 24.h),
                  /// App name
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: CommonImage(strIcon: Assets.svgs.svgTrulynk, imgColor: AppColors.primary),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  // Tagline
                  SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Text(
                        'Where hearts meet.',
                        style: TextStyles.medium.copyWith(
                          fontSize: 22.sp,
                          color: AppColors.black.withValues(alpha: 0.9),
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:dateme/framework/controller/auth/login_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/splash/mobile/splash_mobile.dart';
import 'package:dateme/ui/splash/web/splash_web.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/device_configuration.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';
import 'package:responsive_builder/responsive_builder.dart';

class Splash extends ConsumerStatefulWidget {
  const Splash({super.key});

  @override
  ConsumerState<Splash> createState() => _SplashState();
}

class _SplashState extends ConsumerState<Splash> with SingleTickerProviderStateMixin {


  @override
  Widget build(BuildContext context) {

    return ScreenTypeLayout.builder(
        mobile: (BuildContext context) {
          return const SplashMobile();
        },
        tablet: (BuildContext context) {
          return const SplashWeb();
        },
        desktop: (BuildContext context) {
          return const SplashWeb();
        }
    );
  }
}

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:dateme/framework/controller/chat/chat_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/chat_sdk_manager.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:flutter/material.dart';

class ChatScreen extends ConsumerStatefulWidget {
  const ChatScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  /// TextEditing Controller
  final TextEditingController _messageController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  void scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(scrollController.position.maxScrollExtent, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
      }
    });
  }

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final chatWatch = ref.read(chatController);
      chatWatch.disposeController(isNotify: true);
      if (false) {
        ChatSdkManager.instance.loginUser(
          'uctulynkusr1',
          '007eJxTYHjcnG3TJGpaN417y4Tts6q8537psu5bNfuv7zfZH2uXr0lWYDA2TzQyMDdLTElKszAxNzdOTDQ2MjBNNktNTjJNTEs20ZH0zWgIZGTY5eHIysjAysAIhCC+CoO5qbmFiVGiga6JibGBrqFhmoFuUlJqkq5FWpqZsWkSkG9qDADRXyej',
        );
      } else {
        ChatSdkManager.instance.loginUser(
          'uctulynkusr2',
          '007eJxTYNhU2Nyi+tFcTOXR8tspP6Sqorg7fjg1yq4RzU1ysT5yYo0Cg7F5opGBuVliSlKahYm5uXFiorGRgWmyWWpykmliWrKJiKRvRkMgI8PmD2msjAysDIxACOKrMFgapCWlpCYZ6JqYGBvoGhqmGehamFua66YZmlmkWVimpKSmGgMAKAMmqw==',
        );
      }

      await chatWatch.getAllMessage();
    });
  }

  @override
  Widget build(BuildContext context) {
    final chatWatch = ref.watch(chatController);

    return Scaffold(
      appBar: CommonAppBar(title: 'Chat', isShowBack: false),
      body: Column(
        children: [
          // Message List
          Expanded(
            child: StreamBuilder<List<ChatMessage>>(
              stream: chatWatch.messageStream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(child: Text('No messages'));
                }
                scrollToBottom();
                final messages = snapshot.data!;
                return ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isSender = message.to == 'uctulynkusr1';

                    return Align(
                      alignment: isSender ? Alignment.centerRight : Alignment.centerLeft,
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(color: isSender ? AppColors.primary : Colors.grey[300], borderRadius: BorderRadius.circular(20)),
                        child: Text(message.body.toJson()['content'], style: TextStyle(color: isSender ? Colors.white : Colors.black)),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          // Input Field
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            margin: const EdgeInsets.only(bottom: 16),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(child: CommonInputFormField(textEditingController: _messageController, validator: (str) => null, hintText: 'Type a message...')),
                const SizedBox(width: 8),
                CircleAvatar(
                  backgroundColor: AppColors.primary,
                  child: IconButton(
                    icon: const Icon(Icons.send, color: Colors.white),
                    onPressed: () async {
                      await chatWatch.sendMessage(_messageController.text);
                      _messageController.clear();
                      scrollToBottom();
                    },
                  ),
                ),
              ],
            ),
          ).paddingOnly(bottom: 100),
        ],
      ),
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'navigation_stack_item.freezed.dart';

@freezed
class NavigationStackItem with _$NavigationStackItem {
  const factory NavigationStackItem.splash() = NavigationStackItemSplashPage;

  const factory NavigationStackItem.intro() = NavigationStackItemIntroScreen;

  // Auth
  const factory NavigationStackItem.login() = NavigationStackItemLoginScreen;

  const factory NavigationStackItem.signup() = NavigationStackItemSignupScreen;

  const factory NavigationStackItem.forgotPassword() = NavigationStackItemForgotPasswordScreen;

  const factory NavigationStackItem.verifyOtpScreen() = NavigationStackItemVerifyOtpScreen;

  /// Profile Onboarding

  const factory NavigationStackItem.locationPermission() = NavigationStackItemLocationPermissionScreen;

  const factory NavigationStackItem.getLocation() = NavigationStackItemGetLocationScreen;

  const factory NavigationStackItem.moreAboutYou() = NavigationStackItemMoreAboutYouScreen;

  const factory NavigationStackItem.basicProfile() = NavigationStackItemBasicProfileScreen;

  const factory NavigationStackItem.identityVerification() = NavigationStackItemIdentityVerificationScreen;

  const factory NavigationStackItem.mobileVerification() = NavigationStackItemMobileVerificationScreen;

  const factory NavigationStackItem.aadhaarVerification() = NavigationStackItemAadhaarVerificationScreen;

  const factory NavigationStackItem.liveSelfie() = NavigationStackItemLiveSelfieScreen;

  const factory NavigationStackItem.partnerPreference() = NavigationStackItemPartnerPreferenceScreen;

  const factory NavigationStackItem.completeProfile() = NavigationStackItemCompleteProfileScreen;
  const factory NavigationStackItem.photoUpload() = NavigationStackItemPhotoUploadScreen;

  /// Home
  const factory NavigationStackItem.home() = NavigationStackItemHomeScreen;

  /// CMS
  const factory NavigationStackItem.cms({required String cmsUrl}) = NavigationStackItemCmsScreen;

  const factory NavigationStackItem.policyConfirmation({required String cmsUrl}) = NavigationStackItemPolicyConfirmationScreen;

  /// My Profile
  const factory NavigationStackItem.myProfile() = NavigationStackItemMyProfileScreen;

  /// Wallet
  const factory NavigationStackItem.walletScreen({String? isFrom}) = NavigationStackItemWalletScreen;

  const factory NavigationStackItem.walletTransactions() = NavigationStackItemWalletTransactions;

  const factory NavigationStackItem.withdrawals() = NavigationStackItemWithdrawals;

  const factory NavigationStackItem.mPinScreen({required bool isFromSplash}) = NavigationStackItemMPinScreen;

  const factory NavigationStackItem.settingScreen() = NavigationStackItemSettingScreen;
}

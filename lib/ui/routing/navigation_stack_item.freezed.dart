// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'navigation_stack_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$NavigationStackItem {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NavigationStackItemCopyWith<$Res> {
  factory $NavigationStackItemCopyWith(
    NavigationStackItem value,
    $Res Function(NavigationStackItem) then,
  ) = _$NavigationStackItemCopyWithImpl<$Res, NavigationStackItem>;
}

/// @nodoc
class _$NavigationStackItemCopyWithImpl<$Res, $Val extends NavigationStackItem>
    implements $NavigationStackItemCopyWith<$Res> {
  _$NavigationStackItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NavigationStackItemSplashPageImplCopyWith<$Res> {
  factory _$$NavigationStackItemSplashPageImplCopyWith(
    _$NavigationStackItemSplashPageImpl value,
    $Res Function(_$NavigationStackItemSplashPageImpl) then,
  ) = __$$NavigationStackItemSplashPageImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemSplashPageImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemSplashPageImpl
        >
    implements _$$NavigationStackItemSplashPageImplCopyWith<$Res> {
  __$$NavigationStackItemSplashPageImplCopyWithImpl(
    _$NavigationStackItemSplashPageImpl _value,
    $Res Function(_$NavigationStackItemSplashPageImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemSplashPageImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemSplashPage {
  const _$NavigationStackItemSplashPageImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.splash()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.splash'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemSplashPageImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return splash();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return splash?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (splash != null) {
      return splash();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return splash(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return splash?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (splash != null) {
      return splash(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemSplashPage implements NavigationStackItem {
  const factory NavigationStackItemSplashPage() =
      _$NavigationStackItemSplashPageImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemIntroScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemIntroScreenImplCopyWith(
    _$NavigationStackItemIntroScreenImpl value,
    $Res Function(_$NavigationStackItemIntroScreenImpl) then,
  ) = __$$NavigationStackItemIntroScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemIntroScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemIntroScreenImpl
        >
    implements _$$NavigationStackItemIntroScreenImplCopyWith<$Res> {
  __$$NavigationStackItemIntroScreenImplCopyWithImpl(
    _$NavigationStackItemIntroScreenImpl _value,
    $Res Function(_$NavigationStackItemIntroScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemIntroScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemIntroScreen {
  const _$NavigationStackItemIntroScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.intro()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.intro'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemIntroScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return intro();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return intro?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (intro != null) {
      return intro();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return intro(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return intro?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (intro != null) {
      return intro(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemIntroScreen implements NavigationStackItem {
  const factory NavigationStackItemIntroScreen() =
      _$NavigationStackItemIntroScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemLoginScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemLoginScreenImplCopyWith(
    _$NavigationStackItemLoginScreenImpl value,
    $Res Function(_$NavigationStackItemLoginScreenImpl) then,
  ) = __$$NavigationStackItemLoginScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemLoginScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemLoginScreenImpl
        >
    implements _$$NavigationStackItemLoginScreenImplCopyWith<$Res> {
  __$$NavigationStackItemLoginScreenImplCopyWithImpl(
    _$NavigationStackItemLoginScreenImpl _value,
    $Res Function(_$NavigationStackItemLoginScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemLoginScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemLoginScreen {
  const _$NavigationStackItemLoginScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.login()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.login'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemLoginScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return login();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return login?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return login(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return login?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemLoginScreen implements NavigationStackItem {
  const factory NavigationStackItemLoginScreen() =
      _$NavigationStackItemLoginScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemSignupScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemSignupScreenImplCopyWith(
    _$NavigationStackItemSignupScreenImpl value,
    $Res Function(_$NavigationStackItemSignupScreenImpl) then,
  ) = __$$NavigationStackItemSignupScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemSignupScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemSignupScreenImpl
        >
    implements _$$NavigationStackItemSignupScreenImplCopyWith<$Res> {
  __$$NavigationStackItemSignupScreenImplCopyWithImpl(
    _$NavigationStackItemSignupScreenImpl _value,
    $Res Function(_$NavigationStackItemSignupScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemSignupScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemSignupScreen {
  const _$NavigationStackItemSignupScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.signup()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.signup'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemSignupScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return signup();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return signup?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (signup != null) {
      return signup();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return signup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return signup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (signup != null) {
      return signup(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemSignupScreen implements NavigationStackItem {
  const factory NavigationStackItemSignupScreen() =
      _$NavigationStackItemSignupScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemForgotPasswordScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemForgotPasswordScreenImplCopyWith(
    _$NavigationStackItemForgotPasswordScreenImpl value,
    $Res Function(_$NavigationStackItemForgotPasswordScreenImpl) then,
  ) = __$$NavigationStackItemForgotPasswordScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemForgotPasswordScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemForgotPasswordScreenImpl
        >
    implements _$$NavigationStackItemForgotPasswordScreenImplCopyWith<$Res> {
  __$$NavigationStackItemForgotPasswordScreenImplCopyWithImpl(
    _$NavigationStackItemForgotPasswordScreenImpl _value,
    $Res Function(_$NavigationStackItemForgotPasswordScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemForgotPasswordScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemForgotPasswordScreen {
  const _$NavigationStackItemForgotPasswordScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.forgotPassword()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.forgotPassword'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemForgotPasswordScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return forgotPassword();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return forgotPassword?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (forgotPassword != null) {
      return forgotPassword();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return forgotPassword(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return forgotPassword?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (forgotPassword != null) {
      return forgotPassword(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemForgotPasswordScreen
    implements NavigationStackItem {
  const factory NavigationStackItemForgotPasswordScreen() =
      _$NavigationStackItemForgotPasswordScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemVerifyOtpScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemVerifyOtpScreenImplCopyWith(
    _$NavigationStackItemVerifyOtpScreenImpl value,
    $Res Function(_$NavigationStackItemVerifyOtpScreenImpl) then,
  ) = __$$NavigationStackItemVerifyOtpScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemVerifyOtpScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemVerifyOtpScreenImpl
        >
    implements _$$NavigationStackItemVerifyOtpScreenImplCopyWith<$Res> {
  __$$NavigationStackItemVerifyOtpScreenImplCopyWithImpl(
    _$NavigationStackItemVerifyOtpScreenImpl _value,
    $Res Function(_$NavigationStackItemVerifyOtpScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemVerifyOtpScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemVerifyOtpScreen {
  const _$NavigationStackItemVerifyOtpScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.verifyOtpScreen()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.verifyOtpScreen'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemVerifyOtpScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return verifyOtpScreen();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return verifyOtpScreen?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (verifyOtpScreen != null) {
      return verifyOtpScreen();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return verifyOtpScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return verifyOtpScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (verifyOtpScreen != null) {
      return verifyOtpScreen(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemVerifyOtpScreen
    implements NavigationStackItem {
  const factory NavigationStackItemVerifyOtpScreen() =
      _$NavigationStackItemVerifyOtpScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemLocationPermissionScreenImplCopyWith<
  $Res
> {
  factory _$$NavigationStackItemLocationPermissionScreenImplCopyWith(
    _$NavigationStackItemLocationPermissionScreenImpl value,
    $Res Function(_$NavigationStackItemLocationPermissionScreenImpl) then,
  ) = __$$NavigationStackItemLocationPermissionScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemLocationPermissionScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemLocationPermissionScreenImpl
        >
    implements
        _$$NavigationStackItemLocationPermissionScreenImplCopyWith<$Res> {
  __$$NavigationStackItemLocationPermissionScreenImplCopyWithImpl(
    _$NavigationStackItemLocationPermissionScreenImpl _value,
    $Res Function(_$NavigationStackItemLocationPermissionScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemLocationPermissionScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemLocationPermissionScreen {
  const _$NavigationStackItemLocationPermissionScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.locationPermission()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.locationPermission'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemLocationPermissionScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return locationPermission();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return locationPermission?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (locationPermission != null) {
      return locationPermission();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return locationPermission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return locationPermission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (locationPermission != null) {
      return locationPermission(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemLocationPermissionScreen
    implements NavigationStackItem {
  const factory NavigationStackItemLocationPermissionScreen() =
      _$NavigationStackItemLocationPermissionScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemGetLocationScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemGetLocationScreenImplCopyWith(
    _$NavigationStackItemGetLocationScreenImpl value,
    $Res Function(_$NavigationStackItemGetLocationScreenImpl) then,
  ) = __$$NavigationStackItemGetLocationScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemGetLocationScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemGetLocationScreenImpl
        >
    implements _$$NavigationStackItemGetLocationScreenImplCopyWith<$Res> {
  __$$NavigationStackItemGetLocationScreenImplCopyWithImpl(
    _$NavigationStackItemGetLocationScreenImpl _value,
    $Res Function(_$NavigationStackItemGetLocationScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemGetLocationScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemGetLocationScreen {
  const _$NavigationStackItemGetLocationScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.getLocation()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.getLocation'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemGetLocationScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return getLocation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return getLocation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (getLocation != null) {
      return getLocation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return getLocation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return getLocation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (getLocation != null) {
      return getLocation(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemGetLocationScreen
    implements NavigationStackItem {
  const factory NavigationStackItemGetLocationScreen() =
      _$NavigationStackItemGetLocationScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemMoreAboutYouScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemMoreAboutYouScreenImplCopyWith(
    _$NavigationStackItemMoreAboutYouScreenImpl value,
    $Res Function(_$NavigationStackItemMoreAboutYouScreenImpl) then,
  ) = __$$NavigationStackItemMoreAboutYouScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemMoreAboutYouScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemMoreAboutYouScreenImpl
        >
    implements _$$NavigationStackItemMoreAboutYouScreenImplCopyWith<$Res> {
  __$$NavigationStackItemMoreAboutYouScreenImplCopyWithImpl(
    _$NavigationStackItemMoreAboutYouScreenImpl _value,
    $Res Function(_$NavigationStackItemMoreAboutYouScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemMoreAboutYouScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemMoreAboutYouScreen {
  const _$NavigationStackItemMoreAboutYouScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.moreAboutYou()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.moreAboutYou'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemMoreAboutYouScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return moreAboutYou();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return moreAboutYou?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (moreAboutYou != null) {
      return moreAboutYou();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return moreAboutYou(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return moreAboutYou?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (moreAboutYou != null) {
      return moreAboutYou(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemMoreAboutYouScreen
    implements NavigationStackItem {
  const factory NavigationStackItemMoreAboutYouScreen() =
      _$NavigationStackItemMoreAboutYouScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemBasicProfileScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemBasicProfileScreenImplCopyWith(
    _$NavigationStackItemBasicProfileScreenImpl value,
    $Res Function(_$NavigationStackItemBasicProfileScreenImpl) then,
  ) = __$$NavigationStackItemBasicProfileScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemBasicProfileScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemBasicProfileScreenImpl
        >
    implements _$$NavigationStackItemBasicProfileScreenImplCopyWith<$Res> {
  __$$NavigationStackItemBasicProfileScreenImplCopyWithImpl(
    _$NavigationStackItemBasicProfileScreenImpl _value,
    $Res Function(_$NavigationStackItemBasicProfileScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemBasicProfileScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemBasicProfileScreen {
  const _$NavigationStackItemBasicProfileScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.basicProfile()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.basicProfile'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemBasicProfileScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return basicProfile();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return basicProfile?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (basicProfile != null) {
      return basicProfile();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return basicProfile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return basicProfile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (basicProfile != null) {
      return basicProfile(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemBasicProfileScreen
    implements NavigationStackItem {
  const factory NavigationStackItemBasicProfileScreen() =
      _$NavigationStackItemBasicProfileScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemIdentityVerificationScreenImplCopyWith<
  $Res
> {
  factory _$$NavigationStackItemIdentityVerificationScreenImplCopyWith(
    _$NavigationStackItemIdentityVerificationScreenImpl value,
    $Res Function(_$NavigationStackItemIdentityVerificationScreenImpl) then,
  ) = __$$NavigationStackItemIdentityVerificationScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemIdentityVerificationScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemIdentityVerificationScreenImpl
        >
    implements
        _$$NavigationStackItemIdentityVerificationScreenImplCopyWith<$Res> {
  __$$NavigationStackItemIdentityVerificationScreenImplCopyWithImpl(
    _$NavigationStackItemIdentityVerificationScreenImpl _value,
    $Res Function(_$NavigationStackItemIdentityVerificationScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemIdentityVerificationScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemIdentityVerificationScreen {
  const _$NavigationStackItemIdentityVerificationScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.identityVerification()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.identityVerification'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemIdentityVerificationScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return identityVerification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return identityVerification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (identityVerification != null) {
      return identityVerification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return identityVerification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return identityVerification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (identityVerification != null) {
      return identityVerification(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemIdentityVerificationScreen
    implements NavigationStackItem {
  const factory NavigationStackItemIdentityVerificationScreen() =
      _$NavigationStackItemIdentityVerificationScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemMobileVerificationScreenImplCopyWith<
  $Res
> {
  factory _$$NavigationStackItemMobileVerificationScreenImplCopyWith(
    _$NavigationStackItemMobileVerificationScreenImpl value,
    $Res Function(_$NavigationStackItemMobileVerificationScreenImpl) then,
  ) = __$$NavigationStackItemMobileVerificationScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemMobileVerificationScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemMobileVerificationScreenImpl
        >
    implements
        _$$NavigationStackItemMobileVerificationScreenImplCopyWith<$Res> {
  __$$NavigationStackItemMobileVerificationScreenImplCopyWithImpl(
    _$NavigationStackItemMobileVerificationScreenImpl _value,
    $Res Function(_$NavigationStackItemMobileVerificationScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemMobileVerificationScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemMobileVerificationScreen {
  const _$NavigationStackItemMobileVerificationScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.mobileVerification()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.mobileVerification'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemMobileVerificationScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return mobileVerification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return mobileVerification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (mobileVerification != null) {
      return mobileVerification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return mobileVerification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return mobileVerification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (mobileVerification != null) {
      return mobileVerification(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemMobileVerificationScreen
    implements NavigationStackItem {
  const factory NavigationStackItemMobileVerificationScreen() =
      _$NavigationStackItemMobileVerificationScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemAadhaarVerificationScreenImplCopyWith<
  $Res
> {
  factory _$$NavigationStackItemAadhaarVerificationScreenImplCopyWith(
    _$NavigationStackItemAadhaarVerificationScreenImpl value,
    $Res Function(_$NavigationStackItemAadhaarVerificationScreenImpl) then,
  ) = __$$NavigationStackItemAadhaarVerificationScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemAadhaarVerificationScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemAadhaarVerificationScreenImpl
        >
    implements
        _$$NavigationStackItemAadhaarVerificationScreenImplCopyWith<$Res> {
  __$$NavigationStackItemAadhaarVerificationScreenImplCopyWithImpl(
    _$NavigationStackItemAadhaarVerificationScreenImpl _value,
    $Res Function(_$NavigationStackItemAadhaarVerificationScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemAadhaarVerificationScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemAadhaarVerificationScreen {
  const _$NavigationStackItemAadhaarVerificationScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.aadhaarVerification()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.aadhaarVerification'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemAadhaarVerificationScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return aadhaarVerification();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return aadhaarVerification?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (aadhaarVerification != null) {
      return aadhaarVerification();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return aadhaarVerification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return aadhaarVerification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (aadhaarVerification != null) {
      return aadhaarVerification(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemAadhaarVerificationScreen
    implements NavigationStackItem {
  const factory NavigationStackItemAadhaarVerificationScreen() =
      _$NavigationStackItemAadhaarVerificationScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemLiveSelfieScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemLiveSelfieScreenImplCopyWith(
    _$NavigationStackItemLiveSelfieScreenImpl value,
    $Res Function(_$NavigationStackItemLiveSelfieScreenImpl) then,
  ) = __$$NavigationStackItemLiveSelfieScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemLiveSelfieScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemLiveSelfieScreenImpl
        >
    implements _$$NavigationStackItemLiveSelfieScreenImplCopyWith<$Res> {
  __$$NavigationStackItemLiveSelfieScreenImplCopyWithImpl(
    _$NavigationStackItemLiveSelfieScreenImpl _value,
    $Res Function(_$NavigationStackItemLiveSelfieScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemLiveSelfieScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemLiveSelfieScreen {
  const _$NavigationStackItemLiveSelfieScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.liveSelfie()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.liveSelfie'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemLiveSelfieScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return liveSelfie();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return liveSelfie?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (liveSelfie != null) {
      return liveSelfie();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return liveSelfie(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return liveSelfie?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (liveSelfie != null) {
      return liveSelfie(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemLiveSelfieScreen
    implements NavigationStackItem {
  const factory NavigationStackItemLiveSelfieScreen() =
      _$NavigationStackItemLiveSelfieScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemPartnerPreferenceScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemPartnerPreferenceScreenImplCopyWith(
    _$NavigationStackItemPartnerPreferenceScreenImpl value,
    $Res Function(_$NavigationStackItemPartnerPreferenceScreenImpl) then,
  ) = __$$NavigationStackItemPartnerPreferenceScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemPartnerPreferenceScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemPartnerPreferenceScreenImpl
        >
    implements _$$NavigationStackItemPartnerPreferenceScreenImplCopyWith<$Res> {
  __$$NavigationStackItemPartnerPreferenceScreenImplCopyWithImpl(
    _$NavigationStackItemPartnerPreferenceScreenImpl _value,
    $Res Function(_$NavigationStackItemPartnerPreferenceScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemPartnerPreferenceScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemPartnerPreferenceScreen {
  const _$NavigationStackItemPartnerPreferenceScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.partnerPreference()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.partnerPreference'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemPartnerPreferenceScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return partnerPreference();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return partnerPreference?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (partnerPreference != null) {
      return partnerPreference();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return partnerPreference(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return partnerPreference?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (partnerPreference != null) {
      return partnerPreference(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemPartnerPreferenceScreen
    implements NavigationStackItem {
  const factory NavigationStackItemPartnerPreferenceScreen() =
      _$NavigationStackItemPartnerPreferenceScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemCompleteProfileScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemCompleteProfileScreenImplCopyWith(
    _$NavigationStackItemCompleteProfileScreenImpl value,
    $Res Function(_$NavigationStackItemCompleteProfileScreenImpl) then,
  ) = __$$NavigationStackItemCompleteProfileScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemCompleteProfileScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemCompleteProfileScreenImpl
        >
    implements _$$NavigationStackItemCompleteProfileScreenImplCopyWith<$Res> {
  __$$NavigationStackItemCompleteProfileScreenImplCopyWithImpl(
    _$NavigationStackItemCompleteProfileScreenImpl _value,
    $Res Function(_$NavigationStackItemCompleteProfileScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemCompleteProfileScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemCompleteProfileScreen {
  const _$NavigationStackItemCompleteProfileScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.completeProfile()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.completeProfile'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemCompleteProfileScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return completeProfile();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return completeProfile?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (completeProfile != null) {
      return completeProfile();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return completeProfile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return completeProfile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (completeProfile != null) {
      return completeProfile(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemCompleteProfileScreen
    implements NavigationStackItem {
  const factory NavigationStackItemCompleteProfileScreen() =
      _$NavigationStackItemCompleteProfileScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemPhotoUploadScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemPhotoUploadScreenImplCopyWith(
    _$NavigationStackItemPhotoUploadScreenImpl value,
    $Res Function(_$NavigationStackItemPhotoUploadScreenImpl) then,
  ) = __$$NavigationStackItemPhotoUploadScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemPhotoUploadScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemPhotoUploadScreenImpl
        >
    implements _$$NavigationStackItemPhotoUploadScreenImplCopyWith<$Res> {
  __$$NavigationStackItemPhotoUploadScreenImplCopyWithImpl(
    _$NavigationStackItemPhotoUploadScreenImpl _value,
    $Res Function(_$NavigationStackItemPhotoUploadScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemPhotoUploadScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemPhotoUploadScreen {
  const _$NavigationStackItemPhotoUploadScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.photoUpload()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.photoUpload'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemPhotoUploadScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return photoUpload();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return photoUpload?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (photoUpload != null) {
      return photoUpload();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return photoUpload(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return photoUpload?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (photoUpload != null) {
      return photoUpload(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemPhotoUploadScreen
    implements NavigationStackItem {
  const factory NavigationStackItemPhotoUploadScreen() =
      _$NavigationStackItemPhotoUploadScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemHomeScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemHomeScreenImplCopyWith(
    _$NavigationStackItemHomeScreenImpl value,
    $Res Function(_$NavigationStackItemHomeScreenImpl) then,
  ) = __$$NavigationStackItemHomeScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemHomeScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemHomeScreenImpl
        >
    implements _$$NavigationStackItemHomeScreenImplCopyWith<$Res> {
  __$$NavigationStackItemHomeScreenImplCopyWithImpl(
    _$NavigationStackItemHomeScreenImpl _value,
    $Res Function(_$NavigationStackItemHomeScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemHomeScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemHomeScreen {
  const _$NavigationStackItemHomeScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.home()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.home'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemHomeScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return home();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return home?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (home != null) {
      return home();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return home(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return home?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (home != null) {
      return home(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemHomeScreen implements NavigationStackItem {
  const factory NavigationStackItemHomeScreen() =
      _$NavigationStackItemHomeScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemCmsScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemCmsScreenImplCopyWith(
    _$NavigationStackItemCmsScreenImpl value,
    $Res Function(_$NavigationStackItemCmsScreenImpl) then,
  ) = __$$NavigationStackItemCmsScreenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String cmsUrl});
}

/// @nodoc
class __$$NavigationStackItemCmsScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemCmsScreenImpl
        >
    implements _$$NavigationStackItemCmsScreenImplCopyWith<$Res> {
  __$$NavigationStackItemCmsScreenImplCopyWithImpl(
    _$NavigationStackItemCmsScreenImpl _value,
    $Res Function(_$NavigationStackItemCmsScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? cmsUrl = null}) {
    return _then(
      _$NavigationStackItemCmsScreenImpl(
        cmsUrl:
            null == cmsUrl
                ? _value.cmsUrl
                : cmsUrl // ignore: cast_nullable_to_non_nullable
                    as String,
      ),
    );
  }
}

/// @nodoc

class _$NavigationStackItemCmsScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemCmsScreen {
  const _$NavigationStackItemCmsScreenImpl({required this.cmsUrl});

  @override
  final String cmsUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.cms(cmsUrl: $cmsUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.cms'))
      ..add(DiagnosticsProperty('cmsUrl', cmsUrl));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemCmsScreenImpl &&
            (identical(other.cmsUrl, cmsUrl) || other.cmsUrl == cmsUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cmsUrl);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NavigationStackItemCmsScreenImplCopyWith<
    _$NavigationStackItemCmsScreenImpl
  >
  get copyWith => __$$NavigationStackItemCmsScreenImplCopyWithImpl<
    _$NavigationStackItemCmsScreenImpl
  >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return cms(cmsUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return cms?.call(cmsUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (cms != null) {
      return cms(cmsUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return cms(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return cms?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (cms != null) {
      return cms(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemCmsScreen implements NavigationStackItem {
  const factory NavigationStackItemCmsScreen({required final String cmsUrl}) =
      _$NavigationStackItemCmsScreenImpl;

  String get cmsUrl;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NavigationStackItemCmsScreenImplCopyWith<
    _$NavigationStackItemCmsScreenImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NavigationStackItemPolicyConfirmationScreenImplCopyWith<
  $Res
> {
  factory _$$NavigationStackItemPolicyConfirmationScreenImplCopyWith(
    _$NavigationStackItemPolicyConfirmationScreenImpl value,
    $Res Function(_$NavigationStackItemPolicyConfirmationScreenImpl) then,
  ) = __$$NavigationStackItemPolicyConfirmationScreenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String cmsUrl});
}

/// @nodoc
class __$$NavigationStackItemPolicyConfirmationScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemPolicyConfirmationScreenImpl
        >
    implements
        _$$NavigationStackItemPolicyConfirmationScreenImplCopyWith<$Res> {
  __$$NavigationStackItemPolicyConfirmationScreenImplCopyWithImpl(
    _$NavigationStackItemPolicyConfirmationScreenImpl _value,
    $Res Function(_$NavigationStackItemPolicyConfirmationScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? cmsUrl = null}) {
    return _then(
      _$NavigationStackItemPolicyConfirmationScreenImpl(
        cmsUrl:
            null == cmsUrl
                ? _value.cmsUrl
                : cmsUrl // ignore: cast_nullable_to_non_nullable
                    as String,
      ),
    );
  }
}

/// @nodoc

class _$NavigationStackItemPolicyConfirmationScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemPolicyConfirmationScreen {
  const _$NavigationStackItemPolicyConfirmationScreenImpl({
    required this.cmsUrl,
  });

  @override
  final String cmsUrl;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.policyConfirmation(cmsUrl: $cmsUrl)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
        DiagnosticsProperty('type', 'NavigationStackItem.policyConfirmation'),
      )
      ..add(DiagnosticsProperty('cmsUrl', cmsUrl));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemPolicyConfirmationScreenImpl &&
            (identical(other.cmsUrl, cmsUrl) || other.cmsUrl == cmsUrl));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cmsUrl);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NavigationStackItemPolicyConfirmationScreenImplCopyWith<
    _$NavigationStackItemPolicyConfirmationScreenImpl
  >
  get copyWith =>
      __$$NavigationStackItemPolicyConfirmationScreenImplCopyWithImpl<
        _$NavigationStackItemPolicyConfirmationScreenImpl
      >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return policyConfirmation(cmsUrl);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return policyConfirmation?.call(cmsUrl);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (policyConfirmation != null) {
      return policyConfirmation(cmsUrl);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return policyConfirmation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return policyConfirmation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (policyConfirmation != null) {
      return policyConfirmation(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemPolicyConfirmationScreen
    implements NavigationStackItem {
  const factory NavigationStackItemPolicyConfirmationScreen({
    required final String cmsUrl,
  }) = _$NavigationStackItemPolicyConfirmationScreenImpl;

  String get cmsUrl;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NavigationStackItemPolicyConfirmationScreenImplCopyWith<
    _$NavigationStackItemPolicyConfirmationScreenImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NavigationStackItemMyProfileScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemMyProfileScreenImplCopyWith(
    _$NavigationStackItemMyProfileScreenImpl value,
    $Res Function(_$NavigationStackItemMyProfileScreenImpl) then,
  ) = __$$NavigationStackItemMyProfileScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemMyProfileScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemMyProfileScreenImpl
        >
    implements _$$NavigationStackItemMyProfileScreenImplCopyWith<$Res> {
  __$$NavigationStackItemMyProfileScreenImplCopyWithImpl(
    _$NavigationStackItemMyProfileScreenImpl _value,
    $Res Function(_$NavigationStackItemMyProfileScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemMyProfileScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemMyProfileScreen {
  const _$NavigationStackItemMyProfileScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.myProfile()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.myProfile'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemMyProfileScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return myProfile();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return myProfile?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (myProfile != null) {
      return myProfile();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return myProfile(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return myProfile?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (myProfile != null) {
      return myProfile(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemMyProfileScreen
    implements NavigationStackItem {
  const factory NavigationStackItemMyProfileScreen() =
      _$NavigationStackItemMyProfileScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemWalletScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemWalletScreenImplCopyWith(
    _$NavigationStackItemWalletScreenImpl value,
    $Res Function(_$NavigationStackItemWalletScreenImpl) then,
  ) = __$$NavigationStackItemWalletScreenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String? isFrom});
}

/// @nodoc
class __$$NavigationStackItemWalletScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemWalletScreenImpl
        >
    implements _$$NavigationStackItemWalletScreenImplCopyWith<$Res> {
  __$$NavigationStackItemWalletScreenImplCopyWithImpl(
    _$NavigationStackItemWalletScreenImpl _value,
    $Res Function(_$NavigationStackItemWalletScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isFrom = freezed}) {
    return _then(
      _$NavigationStackItemWalletScreenImpl(
        isFrom:
            freezed == isFrom
                ? _value.isFrom
                : isFrom // ignore: cast_nullable_to_non_nullable
                    as String?,
      ),
    );
  }
}

/// @nodoc

class _$NavigationStackItemWalletScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemWalletScreen {
  const _$NavigationStackItemWalletScreenImpl({this.isFrom});

  @override
  final String? isFrom;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.walletScreen(isFrom: $isFrom)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.walletScreen'))
      ..add(DiagnosticsProperty('isFrom', isFrom));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemWalletScreenImpl &&
            (identical(other.isFrom, isFrom) || other.isFrom == isFrom));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isFrom);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NavigationStackItemWalletScreenImplCopyWith<
    _$NavigationStackItemWalletScreenImpl
  >
  get copyWith => __$$NavigationStackItemWalletScreenImplCopyWithImpl<
    _$NavigationStackItemWalletScreenImpl
  >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return walletScreen(isFrom);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return walletScreen?.call(isFrom);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (walletScreen != null) {
      return walletScreen(isFrom);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return walletScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return walletScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (walletScreen != null) {
      return walletScreen(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemWalletScreen implements NavigationStackItem {
  const factory NavigationStackItemWalletScreen({final String? isFrom}) =
      _$NavigationStackItemWalletScreenImpl;

  String? get isFrom;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NavigationStackItemWalletScreenImplCopyWith<
    _$NavigationStackItemWalletScreenImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NavigationStackItemWalletTransactionsImplCopyWith<$Res> {
  factory _$$NavigationStackItemWalletTransactionsImplCopyWith(
    _$NavigationStackItemWalletTransactionsImpl value,
    $Res Function(_$NavigationStackItemWalletTransactionsImpl) then,
  ) = __$$NavigationStackItemWalletTransactionsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemWalletTransactionsImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemWalletTransactionsImpl
        >
    implements _$$NavigationStackItemWalletTransactionsImplCopyWith<$Res> {
  __$$NavigationStackItemWalletTransactionsImplCopyWithImpl(
    _$NavigationStackItemWalletTransactionsImpl _value,
    $Res Function(_$NavigationStackItemWalletTransactionsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemWalletTransactionsImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemWalletTransactions {
  const _$NavigationStackItemWalletTransactionsImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.walletTransactions()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(
      DiagnosticsProperty('type', 'NavigationStackItem.walletTransactions'),
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemWalletTransactionsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return walletTransactions();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return walletTransactions?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (walletTransactions != null) {
      return walletTransactions();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return walletTransactions(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return walletTransactions?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (walletTransactions != null) {
      return walletTransactions(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemWalletTransactions
    implements NavigationStackItem {
  const factory NavigationStackItemWalletTransactions() =
      _$NavigationStackItemWalletTransactionsImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemWithdrawalsImplCopyWith<$Res> {
  factory _$$NavigationStackItemWithdrawalsImplCopyWith(
    _$NavigationStackItemWithdrawalsImpl value,
    $Res Function(_$NavigationStackItemWithdrawalsImpl) then,
  ) = __$$NavigationStackItemWithdrawalsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemWithdrawalsImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemWithdrawalsImpl
        >
    implements _$$NavigationStackItemWithdrawalsImplCopyWith<$Res> {
  __$$NavigationStackItemWithdrawalsImplCopyWithImpl(
    _$NavigationStackItemWithdrawalsImpl _value,
    $Res Function(_$NavigationStackItemWithdrawalsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemWithdrawalsImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemWithdrawals {
  const _$NavigationStackItemWithdrawalsImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.withdrawals()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.withdrawals'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemWithdrawalsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return withdrawals();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return withdrawals?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (withdrawals != null) {
      return withdrawals();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return withdrawals(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return withdrawals?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (withdrawals != null) {
      return withdrawals(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemWithdrawals implements NavigationStackItem {
  const factory NavigationStackItemWithdrawals() =
      _$NavigationStackItemWithdrawalsImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemMPinScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemMPinScreenImplCopyWith(
    _$NavigationStackItemMPinScreenImpl value,
    $Res Function(_$NavigationStackItemMPinScreenImpl) then,
  ) = __$$NavigationStackItemMPinScreenImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool isFromSplash});
}

/// @nodoc
class __$$NavigationStackItemMPinScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemMPinScreenImpl
        >
    implements _$$NavigationStackItemMPinScreenImplCopyWith<$Res> {
  __$$NavigationStackItemMPinScreenImplCopyWithImpl(
    _$NavigationStackItemMPinScreenImpl _value,
    $Res Function(_$NavigationStackItemMPinScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? isFromSplash = null}) {
    return _then(
      _$NavigationStackItemMPinScreenImpl(
        isFromSplash:
            null == isFromSplash
                ? _value.isFromSplash
                : isFromSplash // ignore: cast_nullable_to_non_nullable
                    as bool,
      ),
    );
  }
}

/// @nodoc

class _$NavigationStackItemMPinScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemMPinScreen {
  const _$NavigationStackItemMPinScreenImpl({required this.isFromSplash});

  @override
  final bool isFromSplash;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.mPinScreen(isFromSplash: $isFromSplash)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.mPinScreen'))
      ..add(DiagnosticsProperty('isFromSplash', isFromSplash));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemMPinScreenImpl &&
            (identical(other.isFromSplash, isFromSplash) ||
                other.isFromSplash == isFromSplash));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isFromSplash);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NavigationStackItemMPinScreenImplCopyWith<
    _$NavigationStackItemMPinScreenImpl
  >
  get copyWith => __$$NavigationStackItemMPinScreenImplCopyWithImpl<
    _$NavigationStackItemMPinScreenImpl
  >(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return mPinScreen(isFromSplash);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return mPinScreen?.call(isFromSplash);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (mPinScreen != null) {
      return mPinScreen(isFromSplash);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return mPinScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return mPinScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (mPinScreen != null) {
      return mPinScreen(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemMPinScreen implements NavigationStackItem {
  const factory NavigationStackItemMPinScreen({
    required final bool isFromSplash,
  }) = _$NavigationStackItemMPinScreenImpl;

  bool get isFromSplash;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NavigationStackItemMPinScreenImplCopyWith<
    _$NavigationStackItemMPinScreenImpl
  >
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NavigationStackItemSettingScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemSettingScreenImplCopyWith(
    _$NavigationStackItemSettingScreenImpl value,
    $Res Function(_$NavigationStackItemSettingScreenImpl) then,
  ) = __$$NavigationStackItemSettingScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemSettingScreenImplCopyWithImpl<$Res>
    extends
        _$NavigationStackItemCopyWithImpl<
          $Res,
          _$NavigationStackItemSettingScreenImpl
        >
    implements _$$NavigationStackItemSettingScreenImplCopyWith<$Res> {
  __$$NavigationStackItemSettingScreenImplCopyWithImpl(
    _$NavigationStackItemSettingScreenImpl _value,
    $Res Function(_$NavigationStackItemSettingScreenImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemSettingScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemSettingScreen {
  const _$NavigationStackItemSettingScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.settingScreen()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.settingScreen'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemSettingScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() intro,
    required TResult Function() login,
    required TResult Function() signup,
    required TResult Function() forgotPassword,
    required TResult Function() verifyOtpScreen,
    required TResult Function() locationPermission,
    required TResult Function() getLocation,
    required TResult Function() moreAboutYou,
    required TResult Function() basicProfile,
    required TResult Function() identityVerification,
    required TResult Function() mobileVerification,
    required TResult Function() aadhaarVerification,
    required TResult Function() liveSelfie,
    required TResult Function() partnerPreference,
    required TResult Function() completeProfile,
    required TResult Function() photoUpload,
    required TResult Function() home,
    required TResult Function(String cmsUrl) cms,
    required TResult Function(String cmsUrl) policyConfirmation,
    required TResult Function() myProfile,
    required TResult Function(String? isFrom) walletScreen,
    required TResult Function() walletTransactions,
    required TResult Function() withdrawals,
    required TResult Function(bool isFromSplash) mPinScreen,
    required TResult Function() settingScreen,
  }) {
    return settingScreen();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? intro,
    TResult? Function()? login,
    TResult? Function()? signup,
    TResult? Function()? forgotPassword,
    TResult? Function()? verifyOtpScreen,
    TResult? Function()? locationPermission,
    TResult? Function()? getLocation,
    TResult? Function()? moreAboutYou,
    TResult? Function()? basicProfile,
    TResult? Function()? identityVerification,
    TResult? Function()? mobileVerification,
    TResult? Function()? aadhaarVerification,
    TResult? Function()? liveSelfie,
    TResult? Function()? partnerPreference,
    TResult? Function()? completeProfile,
    TResult? Function()? photoUpload,
    TResult? Function()? home,
    TResult? Function(String cmsUrl)? cms,
    TResult? Function(String cmsUrl)? policyConfirmation,
    TResult? Function()? myProfile,
    TResult? Function(String? isFrom)? walletScreen,
    TResult? Function()? walletTransactions,
    TResult? Function()? withdrawals,
    TResult? Function(bool isFromSplash)? mPinScreen,
    TResult? Function()? settingScreen,
  }) {
    return settingScreen?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? intro,
    TResult Function()? login,
    TResult Function()? signup,
    TResult Function()? forgotPassword,
    TResult Function()? verifyOtpScreen,
    TResult Function()? locationPermission,
    TResult Function()? getLocation,
    TResult Function()? moreAboutYou,
    TResult Function()? basicProfile,
    TResult Function()? identityVerification,
    TResult Function()? mobileVerification,
    TResult Function()? aadhaarVerification,
    TResult Function()? liveSelfie,
    TResult Function()? partnerPreference,
    TResult Function()? completeProfile,
    TResult Function()? photoUpload,
    TResult Function()? home,
    TResult Function(String cmsUrl)? cms,
    TResult Function(String cmsUrl)? policyConfirmation,
    TResult Function()? myProfile,
    TResult Function(String? isFrom)? walletScreen,
    TResult Function()? walletTransactions,
    TResult Function()? withdrawals,
    TResult Function(bool isFromSplash)? mPinScreen,
    TResult Function()? settingScreen,
    required TResult orElse(),
  }) {
    if (settingScreen != null) {
      return settingScreen();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemIntroScreen value) intro,
    required TResult Function(NavigationStackItemLoginScreen value) login,
    required TResult Function(NavigationStackItemSignupScreen value) signup,
    required TResult Function(NavigationStackItemForgotPasswordScreen value)
    forgotPassword,
    required TResult Function(NavigationStackItemVerifyOtpScreen value)
    verifyOtpScreen,
    required TResult Function(NavigationStackItemLocationPermissionScreen value)
    locationPermission,
    required TResult Function(NavigationStackItemGetLocationScreen value)
    getLocation,
    required TResult Function(NavigationStackItemMoreAboutYouScreen value)
    moreAboutYou,
    required TResult Function(NavigationStackItemBasicProfileScreen value)
    basicProfile,
    required TResult Function(
      NavigationStackItemIdentityVerificationScreen value,
    )
    identityVerification,
    required TResult Function(NavigationStackItemMobileVerificationScreen value)
    mobileVerification,
    required TResult Function(
      NavigationStackItemAadhaarVerificationScreen value,
    )
    aadhaarVerification,
    required TResult Function(NavigationStackItemLiveSelfieScreen value)
    liveSelfie,
    required TResult Function(NavigationStackItemPartnerPreferenceScreen value)
    partnerPreference,
    required TResult Function(NavigationStackItemCompleteProfileScreen value)
    completeProfile,
    required TResult Function(NavigationStackItemPhotoUploadScreen value)
    photoUpload,
    required TResult Function(NavigationStackItemHomeScreen value) home,
    required TResult Function(NavigationStackItemCmsScreen value) cms,
    required TResult Function(NavigationStackItemPolicyConfirmationScreen value)
    policyConfirmation,
    required TResult Function(NavigationStackItemMyProfileScreen value)
    myProfile,
    required TResult Function(NavigationStackItemWalletScreen value)
    walletScreen,
    required TResult Function(NavigationStackItemWalletTransactions value)
    walletTransactions,
    required TResult Function(NavigationStackItemWithdrawals value) withdrawals,
    required TResult Function(NavigationStackItemMPinScreen value) mPinScreen,
    required TResult Function(NavigationStackItemSettingScreen value)
    settingScreen,
  }) {
    return settingScreen(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemIntroScreen value)? intro,
    TResult? Function(NavigationStackItemLoginScreen value)? login,
    TResult? Function(NavigationStackItemSignupScreen value)? signup,
    TResult? Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult? Function(NavigationStackItemVerifyOtpScreen value)?
    verifyOtpScreen,
    TResult? Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult? Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult? Function(NavigationStackItemMoreAboutYouScreen value)?
    moreAboutYou,
    TResult? Function(NavigationStackItemBasicProfileScreen value)?
    basicProfile,
    TResult? Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult? Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult? Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult? Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult? Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult? Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult? Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult? Function(NavigationStackItemHomeScreen value)? home,
    TResult? Function(NavigationStackItemCmsScreen value)? cms,
    TResult? Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult? Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult? Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult? Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult? Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult? Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult? Function(NavigationStackItemSettingScreen value)? settingScreen,
  }) {
    return settingScreen?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemIntroScreen value)? intro,
    TResult Function(NavigationStackItemLoginScreen value)? login,
    TResult Function(NavigationStackItemSignupScreen value)? signup,
    TResult Function(NavigationStackItemForgotPasswordScreen value)?
    forgotPassword,
    TResult Function(NavigationStackItemVerifyOtpScreen value)? verifyOtpScreen,
    TResult Function(NavigationStackItemLocationPermissionScreen value)?
    locationPermission,
    TResult Function(NavigationStackItemGetLocationScreen value)? getLocation,
    TResult Function(NavigationStackItemMoreAboutYouScreen value)? moreAboutYou,
    TResult Function(NavigationStackItemBasicProfileScreen value)? basicProfile,
    TResult Function(NavigationStackItemIdentityVerificationScreen value)?
    identityVerification,
    TResult Function(NavigationStackItemMobileVerificationScreen value)?
    mobileVerification,
    TResult Function(NavigationStackItemAadhaarVerificationScreen value)?
    aadhaarVerification,
    TResult Function(NavigationStackItemLiveSelfieScreen value)? liveSelfie,
    TResult Function(NavigationStackItemPartnerPreferenceScreen value)?
    partnerPreference,
    TResult Function(NavigationStackItemCompleteProfileScreen value)?
    completeProfile,
    TResult Function(NavigationStackItemPhotoUploadScreen value)? photoUpload,
    TResult Function(NavigationStackItemHomeScreen value)? home,
    TResult Function(NavigationStackItemCmsScreen value)? cms,
    TResult Function(NavigationStackItemPolicyConfirmationScreen value)?
    policyConfirmation,
    TResult Function(NavigationStackItemMyProfileScreen value)? myProfile,
    TResult Function(NavigationStackItemWalletScreen value)? walletScreen,
    TResult Function(NavigationStackItemWalletTransactions value)?
    walletTransactions,
    TResult Function(NavigationStackItemWithdrawals value)? withdrawals,
    TResult Function(NavigationStackItemMPinScreen value)? mPinScreen,
    TResult Function(NavigationStackItemSettingScreen value)? settingScreen,
    required TResult orElse(),
  }) {
    if (settingScreen != null) {
      return settingScreen(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemSettingScreen implements NavigationStackItem {
  const factory NavigationStackItemSettingScreen() =
      _$NavigationStackItemSettingScreenImpl;
}

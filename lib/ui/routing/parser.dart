import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/navigation_stack_keys.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

@injectable
class MainRouterInformationParser extends RouteInformationParser<NavigationStack> {
  WidgetRef ref;
  BuildContext context;

  MainRouterInformationParser(@factoryParam this.ref, @factoryParam this.context);

  @override
  Future<NavigationStack> parseRouteInformation(RouteInformation routeInformation) async {
    debugPrint('........URL......${routeInformation.uri}');

    final Uri uri = routeInformation.uri;
    // final queryParams = uri.queryParameters;
    final items = <NavigationStackItem>[];
    debugPrint('Stack Item Count - ${items.length}');
    // String? langStr = queryParams['lang'];

    // if (langStr != null) {
    //   await context.setLocale(Locale(langStr == "english" ? "en" : "hi"));
    // }

    for (var i = 0; i < uri.pathSegments.length; i = i + 1) {
      final key = uri.pathSegments[i];

      switch (key) {
        /// Splash
        case Keys.splash:
          items.add(const NavigationStackItem.splash());
          break;

        /// Auth
        case Keys.intro:
          items.add(const NavigationStackItem.intro());
          break;
        case Keys.login:
          items.add(const NavigationStackItem.login());
          break;
        case Keys.signup:
          items.add(const NavigationStackItem.signup());
          break;
        case Keys.forgotPassword:
          items.add(const NavigationStackItem.forgotPassword());
          break;
        case Keys.verifyOtpScreen:
          items.add(const NavigationStackItem.verifyOtpScreen());
          break;

        /// Profile Onboarding

        case Keys.locationPermission:
          items.add(const NavigationStackItem.locationPermission());
          break;
        case Keys.getLocation:
          items.add(const NavigationStackItem.getLocation());
          break;
        case Keys.moreAboutYou:
          items.add(const NavigationStackItem.moreAboutYou());
          break;
        case Keys.basicProfile:
          items.add(const NavigationStackItem.basicProfile());
          break;
        case Keys.identityVerification:
          items.add(const NavigationStackItem.identityVerification());
          break;
        case Keys.mobileVerification:
          items.add(const NavigationStackItem.mobileVerification());
          break;
        case Keys.aadhaarVerification:
          items.add(const NavigationStackItem.aadhaarVerification());
          break;
        case Keys.liveSelfie:
          items.add(const NavigationStackItem.liveSelfie());
          break;
        case Keys.partnerPreference:
          items.add(const NavigationStackItem.partnerPreference());
          break;
        case Keys.completeProfile:
          items.add(const NavigationStackItem.completeProfile());
          break;
        case Keys.photoUpload:
          items.add(const NavigationStackItem.photoUpload());
          break;

        /// Home
        case Keys.home:
          items.add(const NavigationStackItem.home());
          break;

        /// CMS
        case Keys.cms:
          items.add(const NavigationStackItem.cms(cmsUrl: ''));
          break;
        case Keys.policyConfirmation:
          items.add(const NavigationStackItem.policyConfirmation(cmsUrl: ''));
          break;

        /// My Profile
        case Keys.myProfile:
          items.add(const NavigationStackItem.myProfile());
          break;

        case Keys.walletScreen:
          items.add(const NavigationStackItem.walletScreen());
          break;

        case Keys.withdrawals:
          items.add(const NavigationStackItem.withdrawals());
          break;

        case Keys.mPinScreen:
          items.add(const NavigationStackItem.mPinScreen(isFromSplash: false));
          break;

        case Keys.settingsScreen:
          items.add(const NavigationStackItem.settingScreen());
          break;

        /// Splash
        default:
          items.add(const NavigationStackItem.splash());
        // default:
        //   items.add(const NavigationStackItem.notFound());
      }
    }
    if (items.isEmpty) {
      const fallback = NavigationStackItem.splash();
      if (items.isNotEmpty && items.first is NavigationStackItemSplashPage) {
        items[0] = fallback;
      } else {
        items.insert(0, fallback);
      }
    }
    return NavigationStack(items);
  }

  ///THIS IS IMPORTANT: Here we restore the web history
  @override
  RouteInformation restoreRouteInformation(NavigationStack configuration) {
    final location = configuration.items.fold<String>('', (previousValue, element) {
      return previousValue +
          element.when(
            /// Splash
            splash: () => '/${Keys.splash}',

            /// Auth
            intro: () => '/${Keys.intro}',
            login: () => '/${Keys.login}',
            signup: () => '/${Keys.signup}',
            forgotPassword: () => '/${Keys.forgotPassword}',
            verifyOtpScreen: () => '/${Keys.verifyOtpScreen}',

            /// Profile Onboarding
            locationPermission: () => '/${Keys.locationPermission}',
            getLocation: () => '/${Keys.getLocation}',
            moreAboutYou: () => '/${Keys.moreAboutYou}',
            basicProfile: () => '/${Keys.basicProfile}',
            identityVerification: () => '/${Keys.identityVerification}',
            mobileVerification: () => '/${Keys.mobileVerification}',
            aadhaarVerification: () => '/${Keys.aadhaarVerification}',
            liveSelfie: () => '/${Keys.liveSelfie}',
            partnerPreference: () => '/${Keys.partnerPreference}',
            completeProfile: () => '/${Keys.completeProfile}',
            photoUpload: () => '/${Keys.photoUpload}',

            /// Home
            home: () => '/${Keys.home}',

            /// CMS
            cms: (cmsUrl) => '/${Keys.cms}',
            policyConfirmation: (cmsUrl) => '/${Keys.policyConfirmation}',

            /// My Profile
            myProfile: () => '/${Keys.myProfile}',

            /// Wallet
            walletScreen: (isFrom) => '/${Keys.walletScreen}',
            walletTransactions: () => '/${Keys.walletTransactions}',
            withdrawals: () => '/${Keys.withdrawals}',

            ///Mpin
            mPinScreen: (isFromSplash) => '/${Keys.mPinScreen}',
            settingScreen: () => '/${Keys.settingsScreen}',
          );
    });
    return RouteInformation(uri: Uri.parse(location));
  }
}

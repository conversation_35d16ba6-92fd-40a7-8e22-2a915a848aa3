import 'package:dateme/ui/auth/login_screen.dart';
import 'package:dateme/ui/auth/signup_screen.dart';
import 'package:dateme/ui/auth/verify_otp_screen.dart';
import 'package:dateme/ui/cms/cms_screen.dart';
import 'package:dateme/ui/dashboard/dashboard_screen.dart';
import 'package:dateme/ui/forgot_password/forgot_password.dart';
import 'package:dateme/ui/intro/intro_screen.dart';
import 'package:dateme/ui/mpin/mpin_screen.dart';
import 'package:dateme/ui/my_profile/my_profile_screen.dart';
import 'package:dateme/ui/profile_onboarding/basic_profile_screen.dart';
import 'package:dateme/ui/profile_onboarding/complete_profile_onboarding_screen.dart';
import 'package:dateme/ui/profile_onboarding/get_location_screen.dart';
import 'package:dateme/ui/profile_onboarding/identity_verification/aadhaar_verification_screen.dart';
import 'package:dateme/ui/profile_onboarding/identity_verification/identity_verification_screen.dart';
import 'package:dateme/ui/profile_onboarding/identity_verification/mobile_verification_screen.dart';
import 'package:dateme/ui/profile_onboarding/live_photo_detection_screen.dart';
import 'package:dateme/ui/profile_onboarding/location_permission_screen.dart';
import 'package:dateme/ui/profile_onboarding/more_about_you_screen.dart';
import 'package:dateme/ui/profile_onboarding/partner_preference_screen.dart';
import 'package:dateme/ui/profile_onboarding/photo_upload_screen.dart';
import 'package:dateme/ui/profile_onboarding/policy_confirmation_screen.dart';
import 'package:dateme/ui/profile_onboarding/profile_onboarding_screen.dart';
import 'package:dateme/ui/routing/navigation_stack_keys.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/settings/settings_screen.dart';
import 'package:dateme/ui/splash/splash.dart';
import 'package:dateme/ui/wallet/transaction_history_screen.dart';
import 'package:dateme/ui/wallet/wallet_screen.dart';
import 'package:dateme/ui/wallet/withdrawl_list_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

final globalNavigatorKey = GlobalKey<NavigatorState>();

@injectable
class MainRouterDelegate extends RouterDelegate<NavigationStack> with ChangeNotifier, PopNavigatorRouterDelegateMixin {
  final NavigationStack stack;

  @override
  void dispose() {
    stack.removeListener(notifyListeners);
    super.dispose();
  }

  MainRouterDelegate(@factoryParam this.stack) : super() {
    stack.addListener(notifyListeners);
  }

  @override
  final navigatorKey = globalNavigatorKey;

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        return Navigator(
          key: navigatorKey,
          pages: _pages(ref),

          /// callback when a page is popped.
          onPopPage: (route, result) {
            /// let the OS handle the back press if there was nothing to pop
            if (!route.didPop(result)) {
              return false;
            }

            /// if we are on root, let OS close app
            if (stack.items.length == 1) return false;

            /// if we are on root, let OS close app
            if (stack.items.isEmpty) return false;

            /// otherwise, pop the stack and consume the event
            stack.pop();
            return true;
          },
        );
      },
    );
  }

  List<Page> _pages(WidgetRef ref) =>
      stack.items
          .mapIndexed(
            (e, i) => e.when(
              splash: () => const NoAnimationPage(child: Splash(), key: ValueKey(Keys.splash)),
              intro: () => const NoAnimationPage(child: IntroScreen(), key: ValueKey(Keys.intro)),
              login: () => const NoAnimationPage(child: LoginScreen(), key: ValueKey(Keys.login)),
              signup: () => const NoAnimationPage(child: SignupScreen(), key: ValueKey(Keys.signup)),
              forgotPassword: () => const NoAnimationPage(child: ForgotPassword(), key: ValueKey(Keys.forgotPassword)),
              verifyOtpScreen: () => const NoAnimationPage(child: VerifyOtpScreen(), key: ValueKey(Keys.verifyOtpScreen)),

              /// Profile Onboarding
              locationPermission: () => const NoAnimationPage(child: LocationPermissionScreen(), key: ValueKey(Keys.locationPermission)),
              getLocation: () => const NoAnimationPage(child: GetLocationScreen(), key: ValueKey(Keys.getLocation)),
              moreAboutYou: () => const NoAnimationPage(child: MoreAboutYouScreen(), key: ValueKey(Keys.moreAboutYou)),
              basicProfile: () => const NoAnimationPage(child: BasicProfileScreen(), key: ValueKey(Keys.basicProfile)),
              identityVerification: () => const NoAnimationPage(child: IdentityVerificationScreen(), key: ValueKey(Keys.identityVerification)),
              mobileVerification: () => const NoAnimationPage(child: MobileVerificationScreen(), key: ValueKey(Keys.mobileVerification)),
              aadhaarVerification: () => const NoAnimationPage(child: AadhaarVerificationScreen(), key: ValueKey(Keys.aadhaarVerification)),
              liveSelfie: () => const NoAnimationPage(child: SimplifiedSelfieScreen(), key: ValueKey(Keys.aadhaarVerification)),
              partnerPreference: () => const NoAnimationPage(child: PartnerPreferenceScreen(), key: ValueKey(Keys.partnerPreference)),
              completeProfile: () => const NoAnimationPage(child: CompleteProfileOnboardingScreen(), key: ValueKey(Keys.completeProfile)),
              photoUpload: () => NoAnimationPage(child: PhotoUploadScreen(), key: ValueKey(Keys.photoUpload)),

              /// Home
              home: () => const NoAnimationPage(child: DashBoard(), key: ValueKey(Keys.home)),

              /// CMS
              cms: (cmsUrl) => NoAnimationPage(child: CmsScreen(cmsUrl: cmsUrl), key: ValueKey(Keys.cms)),
              policyConfirmation: (cmsUrl) => NoAnimationPage(child: PolicyConfirmationScreen(cmsUrl: cmsUrl), key: ValueKey(Keys.policyConfirmation)),

              /// My Profile
              myProfile: () => NoAnimationPage(child: MyProfileScreen(), key: ValueKey(Keys.myProfile)),

              /// Wallet
              walletScreen: (isFrom) => NoAnimationPage(child: WalletScreen(isFrom: isFrom,), key: ValueKey(Keys.walletScreen)),
              walletTransactions: () => NoAnimationPage(child: TransactionHistoryScreen(), key: ValueKey(Keys.walletTransactions)),
              withdrawals: () => NoAnimationPage(child: WithdrawalListScreen(), key: ValueKey(Keys.withdrawals)),

              mPinScreen: (isFromSplash) => NoAnimationPage(child: MpinScreen(isFromSplash: isFromSplash), key: ValueKey(Keys.mPinScreen)),
              settingScreen: () => NoAnimationPage(child: SettingsScreen(), key: ValueKey(Keys.settingsScreen)),
            ),
          )
          .toList();

  @override
  NavigationStack get currentConfiguration => stack;

  @override
  Future<void> setNewRoutePath(NavigationStack configuration) async {
    stack.items = configuration.items;
  }
}

class NoAnimationPage<T> extends Page<T> {
  final Widget child;

  const NoAnimationPage({required this.child, required super.key});

  @override
  Route<T> createRoute(BuildContext context) {
    return PageRouteBuilder<T>(
      settings: this,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionDuration: Duration.zero, // Removes push animation
      reverseTransitionDuration: Duration.zero, // Removes pop animation
    );
  }
}

extension _IndexedIterable<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }
}

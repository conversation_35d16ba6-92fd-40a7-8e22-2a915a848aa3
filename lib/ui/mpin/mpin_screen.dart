import 'package:dateme/framework/controller/auth/login_controller.dart';
import 'package:dateme/framework/controller/mpin/mpin_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MpinScreen extends ConsumerStatefulWidget {
  final bool isFromSplash;

  const MpinScreen({super.key, required this.isFromSplash});

  @override
  ConsumerState<MpinScreen> createState() => _MpinScreenState();
}

class _MpinScreenState extends ConsumerState<MpinScreen> with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    final mpinWatch = ref.read(mpinController);
    mpinWatch.animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    mpinWatch.dotAnimationControllers = List.generate(
      mpinWatch.mpinLength,
      (index) => AnimationController(vsync: this, duration: const Duration(milliseconds: 200)),
    );
    SchedulerBinding.instance.addPostFrameCallback((_) {
      mpinWatch.disposeController(isNotify: true);
      AnalyticsEvents.setMin.track();
    });
  }

  @override
  Widget build(BuildContext context) {
    final mpinWatch = ref.watch(mpinController);
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CommonAppBar(title: 'Set MPIN', isShowBack: true, bgColor: AppColors.white),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary.withAlpha(13),
              AppColors.primary.withAlpha(26),
              AppColors.primary.withAlpha(38),
            ],
          ),
        ),
        child: bodyWidget(),
      ),
    );
  }

  Widget bodyWidget() {
    final mpinWatch = ref.read(mpinController);
    return Column(
      children: [
        SizedBox(height: 32.h),
        _buildMpinDisplay(),
        SizedBox(height: 48.h),
        Expanded(
          child: Container(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: ['1', '2', '3'].map((e) => _buildKeypadButton(e)).toList(),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: ['4', '5', '6'].map((e) => _buildKeypadButton(e)).toList(),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: ['7', '8', '9'].map((e) => _buildKeypadButton(e)).toList(),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildKeypadButton('', isEnabled: false),
                    _buildKeypadButton('0'),
                    _buildBackspaceButton(),
                  ],
                ),
                SizedBox(height: 16.h),
                _buildConfirmButton().paddingOnly(bottom: 20.h),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMpinDisplay() {
    final mpinWatch = ref.read(mpinController);
    return Hero(
      tag: 'mpin_display',
      child: Column(
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 500),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(16.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(26),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      CommonText(
                        title: 'Enter 4-digit MPIN',
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w600,
                        clrfont: AppColors.black0E0E0E,
                      ),
                      SizedBox(height: 8.h),
                      CommonText(
                        title: 'Please set your secure 4-digit MPIN',
                        fontSize: 14.sp,
                        clrfont: AppColors.grey8A8A8A,
                      ),
                      SizedBox(height: 24.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(mpinWatch.mpinLength, (index) => _buildDot(index)),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDot(int index) {
    final mpinWatch = ref.read(mpinController);
    return AnimatedBuilder(
      animation: mpinWatch.dotAnimationControllers[index],
      builder: (context, child) {
        return TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 200),
          tween: Tween(begin: 1.0, end: index < mpinWatch.mpin.length ? 1.2 : 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 8.w),
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: index < mpinWatch.mpin.length ? AppColors.buttonColor : AppColors.white,
                  border: Border.all(
                    color: mpinWatch.showError ? AppColors.red : AppColors.buttonColor,
                    width: 2,
                  ),
                  boxShadow:
                      index < mpinWatch.mpin.length
                          ? [
                            BoxShadow(
                              color: AppColors.buttonColor.withAlpha(77),
                              blurRadius: 8,
                              spreadRadius: 2,
                            ),
                          ]
                          : null,
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildKeypadButton(String number, {bool isEnabled = true}) {
    final mpinWatch = ref.read(mpinController);
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 200),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: AppColors.buttonColor.withAlpha(26),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: isEnabled ? () => _onNumberPress(number) : null,
                customBorder: const CircleBorder(),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.buttonColor, width: 2),
                  ),
                  child: Center(
                    child: CommonText(
                      title: number,
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      clrfont: AppColors.black0E0E0E,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBackspaceButton() {
    final mpinWatch = ref.read(mpinController);
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 200),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.white,
              boxShadow: [
                BoxShadow(
                  color: AppColors.buttonColor.withAlpha(26),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  if (mpinWatch.mpin.isNotEmpty) {
                    mpinWatch.mpin = mpinWatch.mpin.substring(0, mpinWatch.mpin.length - 1);
                    mpinWatch.showError = false;
                    mpinWatch.updateWidget();
                  }
                },
                customBorder: const CircleBorder(),
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.buttonColor, width: 2),
                  ),
                  child: Center(
                    child: Icon(Icons.backspace_outlined, color: AppColors.black0E0E0E, size: 24.sp),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onNumberPress(String number) {
    final mpinWatch = ref.read(mpinController);
    if (mpinWatch.mpin.length < mpinWatch.mpinLength) {
      mpinWatch.mpin += number;
      mpinWatch.dotAnimationControllers[mpinWatch.mpin.length - 1].forward(from: 0.0);
      mpinWatch.showError = false;
      mpinWatch.updateWidget();
    }
  }

  Future<void> _confirmMpin() async {
    final mpinWatch = ref.read(mpinController);
    if (mpinWatch.mpin.length == mpinWatch.mpinLength) {
      if (widget.isFromSplash) {
        await mpinWatch.loginMPin();
        await Future.delayed(Duration(milliseconds: 100), () {
          if (mpinWatch.loginMPinState.success?.accessToken != '' &&
              mpinWatch.loginMPinState.success?.accessToken != null) {
            if (Session.getUserAccessToken().isNotEmpty && !Session.getIsEmailVerified()) {
              ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.login());
            } else if (Session.getUserAccessToken().isNotEmpty && Session.getIsProfileComplete()) {
              ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.home());
            } else if (Session.getUserAccessToken().isNotEmpty && !Session.getIsProfileComplete()) {
              ref.read(loginController).getUserProfile(context, ref);
              // ref
              //     .read(navigationStackController)
              //     .pushAndRemoveAll(const NavigationStackItem.moreAboutYou());
            } else {
              ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.intro());
            }
          } else {
            mpinWatch.mpin = '';
            mpinWatch.strOldMPin = '';
            mpinWatch.isVerifyMPin = false;
            mpinWatch.updateWidget();
          }
        });
      } else if (Session.isMPinSet() && mpinWatch.strOldMPin == '' && widget.isFromSplash == false) {
        await mpinWatch.loginMPin();
        await Future.delayed(Duration(milliseconds: 100), () {
          if (mpinWatch.loginMPinState.success?.accessToken != '') {
            Session.saveLocalData(keyMPin, true);
          }
        });
      } else if (Session.isMPinSet() && widget.isFromSplash == false) {
        await mpinWatch.changeMPin();
        await Future.delayed(Duration(milliseconds: 100), () {
          if (mpinWatch.changeMPinState.success?.success ?? false) {
            Session.saveLocalData(keyMPin, true);
            ref.read(navigationStackController).pop();
          }
          // commonToaster(mpinWatch.changeMPinState.success?.message ?? '');
        });
      } else if (mpinWatch.isVerifyMPin) {
        await mpinWatch.verifyMPin();
        await Future.delayed(Duration(milliseconds: 100), () {
          if (mpinWatch.verifyMPinState.success?.success ?? false) {
            Session.saveLocalData(keyMPin, true);
            ref.read(navigationStackController).pop();
          } else {
            // commonToaster(mpinWatch.verifyMPinState.success?.message ?? '');
          }
        });
      } else {
        await mpinWatch.setMPin();
      }
    }
  }

  Widget _buildConfirmButton() {
    final mpinWatch = ref.read(mpinController);
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: CommonButton(
            buttonText:
                widget.isFromSplash
                    ? 'Login MPIN'
                    : Session.isMPinSet() && mpinWatch.strOldMPin == ''
                    ? 'Change MPIN'
                    : Session.isMPinSet()
                    ? 'Set New MPIN'
                    : mpinWatch.isVerifyMPin
                    ? 'Confirm MPIN'
                    : 'Set MPIN',
            height: 56.h,
            backgroundColor:
                mpinWatch.mpin.length == mpinWatch.mpinLength ? AppColors.buttonColor : AppColors.grey8A8A8A,
            onTap: mpinWatch.mpin.length == mpinWatch.mpinLength ? _confirmMpin : null,
            showLoader:
                mpinWatch.loginMPinState.isLoading ||
                mpinWatch.setMPinState.isLoading ||
                mpinWatch.verifyMPinState.isLoading ||
                mpinWatch.changeMPinState.isLoading,
          ),
        );
      },
    );
  }
}

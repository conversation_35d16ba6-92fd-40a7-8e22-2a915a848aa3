import 'package:dateme/framework/utils/extension/context_extension.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class IntroScreen extends ConsumerStatefulWidget {
  const IntroScreen({super.key});

  @override
  ConsumerState<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends ConsumerState<IntroScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: commonBackgroundDecoration(
        context,
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              /// Top section with circular images
              Expanded(
                flex: 3,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Positioned(
                    //   top: 0,
                    //   right: 30,
                    //   child: CircleAvatar(
                    //     radius: 40,
                    //     backgroundImage: AssetImage('assets/images/person1.jpg'),
                    //   ),
                    // ),
                    // Positioned(
                    //   top: 80,
                    //   left: 10,
                    //   child: CircleAvatar(
                    //     radius: 60,
                    //     backgroundImage: AssetImage('assets/images/person2.jpg'),
                    //   ),
                    // ),
                    //
                    // Positioned(
                    //   top: 100,
                    //   right: 10,
                    //   child: CircleAvatar(
                    //     radius: 60,
                    //     backgroundImage: AssetImage('assets/images/person2.jpg'),
                    //   ),
                    // ),
                    // Positioned(
                    //   top: 300,
                    //   left: 20,
                    //   child: CircleAvatar(
                    //     radius: 50,
                    //     backgroundImage: AssetImage('assets/images/person3.jpg'),
                    //   ),
                    // ),
                    // Positioned(
                    //   bottom: 120,
                    //   right: 50,
                    //   child: CircleAvatar(
                    //     radius: 40,
                    //     backgroundImage: AssetImage('assets/images/person4.jpg'),
                    //   ),
                    // ),
                    // Positioned(
                    //   bottom: 120,
                    //   left: 50,
                    //   child: CircleAvatar(
                    //     radius: 70,
                    //     backgroundImage: AssetImage('assets/images/person4.jpg'),
                    //   ),
                    // ),
                    // Positioned(
                    //   top: 300,
                    //   right: 30,
                    //   child: CircleAvatar(
                    //     radius: 40,
                    //     backgroundImage: AssetImage('assets/images/person4.jpg'),
                    //   ),
                    // ),
                    SizedBox(
                      height: context.height,
                      width: context.width,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(height: 120.h),
                          CommonImage(strIcon: Assets.svgs.svgAppIcon, width: 110, height: 110, boxFit: BoxFit.cover, imgColor: AppColors.primary),
                          CommonImage(strIcon: Assets.svgs.svgTrulynk, width: 100, height: 100,imgColor: AppColors.primary,),
                          const Spacer(),

                          /// Middle section with text
                          CommonImage(strIcon: Assets.svgs.findYourPerfectMatch, width: 200, height: 200).paddingOnly(bottom: 30.h),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              /// Bottom Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: CommonButton(
                      height: 60.h,
                      onTap: () {
                        ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.login());
                      },
                      buttonText: LocaleKeys.keySignIn,
                      backgroundColor: AppColors.transparent,
                      borderColor: AppColors.primary,
                      buttonTextColor: AppColors.primary,
                      borderWidth: 1.sp,
                    ),
                  ),
                  SizedBox(width: 30.w),
                  Expanded(
                    child: CommonButton(
                      height: 60.h,
                      onTap: () {
                        ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.signup());
                      },
                      buttonText: LocaleKeys.keySignup,
                      backgroundColor: AppColors.buttonColor,
                    ),
                  ),
                ],
              ).paddingOnly(bottom: 20.h, left: 20.w, right: 20.w),
            ],
          ),
        ),
      ),
    );
  }
}

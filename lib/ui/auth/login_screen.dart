import 'package:dateme/framework/controller/auth/login_controller.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/device_configuration.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(loginController);
    mobileDeviceConfiguration(context);
    return Scaffold(
      backgroundColor: AppColors.white,
      body: commonBackgroundDecoration(
        context,
        child: SafeArea(
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: 40.h),
                        // App Logo
                        /// App Logo
                        Lottie.asset(
                          Assets.animation.appLogo,
                          fit: BoxFit.fitHeight,
                          height: 100.h,
                          width: 100.h,
                          repeat: false,
                        ),
                        SizedBox(height: 32.h),

                        /// Welcome Text
                        Text(
                          LocaleKeys.keyWelcome,
                          style: TextStyles.extraBold.copyWith(fontSize: 28.sp, color: AppColors.black),
                        ),
                        SizedBox(height: 8.h),

                        /// Sign In Text
                        Text(
                          LocaleKeys.keySignInToContinue,
                          style: TextStyles.regular.copyWith(fontSize: 16.sp, color: AppColors.black),
                        ),
                        SizedBox(height: 40.h),

                        /// Email Field
                        CommonInputFormField(
                          textEditingController: controller.emailController,
                          textInputType: TextInputType.emailAddress,
                          hintText: LocaleKeys.keyEmail,
                          prefixWidget: Icon(Icons.email_outlined, color: AppColors.black),

                          validator: (value) {
                            return controller.validateEmail(value);
                          },
                        ),
                        SizedBox(height: 20.h),

                        /// Password Field
                        CommonInputFormField(
                          textEditingController: controller.passwordController,
                          obscureText: !controller.isPasswordVisible,
                          hintText: LocaleKeys.keyPassword,

                          prefixWidget: Icon(Icons.lock_outline, color: AppColors.black),
                          suffixWidget: IconButton(
                            icon: Icon(
                              controller.isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                              color: AppColors.black,
                            ),
                            onPressed: () {
                              controller.togglePasswordVisibility();
                            },
                          ),

                          validator: (value) {
                            return controller.validatePassword(value);
                          },
                        ),
                        SizedBox(height: 16.h),

                        /// Forgot Password
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: () {
                              /// Handle forgot password
                              ref
                                  .read(navigationStackController)
                                  .push(const NavigationStackItem.forgotPassword());
                            },
                            child: Text(
                              LocaleKeys.keyForgotPassword,
                              style: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.black),
                            ),
                          ),
                        ),
                        SizedBox(height: 24.h),
                        // Login Button
                        CommonButton(
                          buttonText: LocaleKeys.keySignIn,
                          height: 52.h,
                          onTap: () {
                            if (controller.formKey.currentState!.validate()) {
                              /// Track login event
                              AnalyticsEvents.signIn.track(
                                parameters: {'email': controller.emailController.text},
                              );

                              /// Handle login
                              controller.login(context, ref);
                            }
                          },
                        ),
                        SizedBox(height: 24.h),
                        // Sign Up Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.keyNoAccount,
                              style: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.black),
                            ),
                            TextButton(
                              onPressed: () {
                                // Handle navigation to sign up
                                ref
                                    .read(navigationStackController)
                                    .pushAndRemoveAll(const NavigationStackItem.signup());
                              },
                              child: Text(
                                LocaleKeys.keySignup,
                                style: TextStyles.semiBold.copyWith(fontSize: 14.sp, color: AppColors.black),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              DialogProgressBar(isLoading: controller.isLoading),
            ],
          ),
        ),
      ),
    );
  }
}

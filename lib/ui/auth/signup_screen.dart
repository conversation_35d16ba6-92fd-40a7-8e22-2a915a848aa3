import 'package:dateme/framework/controller/auth/signup_controller.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/device_configuration.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_dropdown.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';

class SignupScreen extends ConsumerStatefulWidget {
  const SignupScreen({super.key});

  @override
  ConsumerState<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends ConsumerState<SignupScreen> {
  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(signupController);
    mobileDeviceConfiguration(context);
    return Scaffold(
      backgroundColor: AppColors.white,
      body: commonBackgroundDecoration(
        context,
        child: SafeArea(
          child: Stack(
            children: [
              SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: controller.formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: 40.h),

                        /// App Logo
                        Lottie.asset(
                          Assets.animation.appLogo,
                          fit: BoxFit.fitHeight,
                          height: 80.h,
                          width: 80.h,
                          repeat: false,
                        ),
                        SizedBox(height: 24.h),

                        /// Header
                        Text(
                          LocaleKeys.keyCreateAccount,
                          style: TextStyles.extraBold.copyWith(fontSize: 28.sp, color: AppColors.black),
                        ),
                        SizedBox(height: 8.h),

                        /// Description
                        Text(
                          LocaleKeys.keyFindMatch,
                          style: TextStyles.regular.copyWith(
                            fontSize: 16.sp,
                            color: AppColors.black.withAlpha(200),
                          ),
                        ),
                        SizedBox(height: 32.h),

                        /// Name Field
                        CommonInputFormField(
                          textEditingController: controller.nameController,
                          hintText: LocaleKeys.keyFullName,
                          prefixWidget: Icon(Icons.person_outline, color: AppColors.black),
                          validator: (value) => controller.validateName(value),
                        ),
                        SizedBox(height: 16.h),

                        /// Email Field
                        CommonInputFormField(
                          textEditingController: controller.emailController,
                          textInputType: TextInputType.emailAddress,
                          hintText: LocaleKeys.keyEmail,
                          prefixWidget: Icon(Icons.email_outlined, color: AppColors.black),
                          validator: (value) => controller.validateEmail(value),
                        ),
                        SizedBox(height: 16.h),

                        /// Date of Birth Field
                        CommonInputFormField(
                          textEditingController: controller.dobController,
                          readOnly: true,
                          hintText: LocaleKeys.keyDateOfBirth,
                          prefixWidget: Icon(Icons.calendar_today, color: AppColors.black),
                          onTap: () => controller.selectDate(context),
                          validator: (value) => controller.validateDob(value),
                        ),
                        SizedBox(height: 16.h),

                        /// Gender Selection
                        CommonDropdown(
                          value: controller.selectedGender,
                          hint: LocaleKeys.keySelectGender,
                          items: ['Male', 'Female'],
                          onChanged: (value) => controller.updateGender(value),
                          validator: (value) => controller.validateGender(value),
                          prefixIcon: Icons.people_outline,
                          dropdownColor: AppColors.white,
                          textColor: AppColors.black,
                        ),
                        SizedBox(height: 16.h),

                        /// Password Field
                        CommonInputFormField(
                          textEditingController: controller.passwordController,
                          obscureText: !controller.isPasswordVisible,
                          hintText: LocaleKeys.keyPassword,
                          prefixWidget: Icon(Icons.lock_outline, color: AppColors.black),
                          suffixWidget: IconButton(
                            icon: Icon(
                              controller.isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                              color: AppColors.black,
                            ),
                            onPressed: () => controller.togglePasswordVisibility(),
                          ),
                          validator: (value) => controller.validatePassword(value),
                        ),
                        SizedBox(height: 16.h),

                        //
                        // /// Confirm Password Field
                        // CommonInputFormField(
                        //   textEditingController: controller.confirmPasswordController,
                        //   obscureText: !controller.isConfirmPasswordVisible,
                        //   hintText: LocaleKeys.keyConfirmPassword,
                        //   prefixWidget: Icon(Icons.lock_outline, color: AppColors.black),
                        //   suffixWidget: IconButton(
                        //     icon: Icon(
                        //       controller.isConfirmPasswordVisible
                        //           ? Icons.visibility
                        //           : Icons.visibility_off,
                        //       color: AppColors.black,
                        //     ),
                        //     onPressed: () => controller.toggleConfirmPasswordVisibility(),
                        //   ),
                        //   validator: (value) => controller.validatePassword(value),
                        // ),
                        SizedBox(height: 30.h),

                        /// Signup Button
                        CommonButton(
                          buttonText: LocaleKeys.keySignup,
                          height: 52.h,
                          onTap: () {
                            if (controller.formKey.currentState!.validate()) {
                              /// Track signup event
                              AnalyticsEvents.signUp.track(
                                parameters: {'email': controller.emailController.text},
                              );

                              /// Handle signup
                              controller.signup(ref);
                            }
                          },
                        ),
                        SizedBox(height: 24.h),

                        /// Login Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              LocaleKeys.keyHaveAccount,
                              style: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.black),
                            ),
                            TextButton(
                              onPressed: () {
                                // Navigate to login
                                ref
                                    .read(navigationStackController)
                                    .pushAndRemoveAll(const NavigationStackItem.login());
                              },
                              child: Text(
                                LocaleKeys.keySignIn,
                                style: TextStyles.semiBold.copyWith(fontSize: 14.sp, color: AppColors.black),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                      ],
                    ),
                  ),
                ),
              ),
              DialogProgressBar(isLoading: controller.isLoading),
            ],
          ),
        ),
      ),
    );
  }
}

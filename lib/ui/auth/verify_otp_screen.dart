import 'package:dateme/framework/controller/auth/signup_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/main.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/device_configuration.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/app_toast.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lottie/lottie.dart';

class VerifyOtpScreen extends ConsumerStatefulWidget {
  const VerifyOtpScreen({super.key});

  @override
  ConsumerState<VerifyOtpScreen> createState() => _VerifyOtpScreenState();
}

class _VerifyOtpScreenState extends ConsumerState<VerifyOtpScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool isEdit = false;
  TextEditingController emailCtr = TextEditingController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 800));
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeIn));
    _animationController.forward();

    /// Otp Verification Event Tracking
    AnalyticsEvents.otpVerification.track();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(signupController);
    mobileDeviceConfiguration(context);

    return Scaffold(
      backgroundColor: AppColors.white,
      // appBar: CommonAppBar(title: 'OTP Verification', isCenterTitle: true, isShowBack: true),
      body: commonBackgroundDecoration(
        context,
        child: SafeArea(
          child: Stack(
            children: [
              SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: controller.otpFormKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: 40.h),
                        Container(
                          height: 220.h,
                          width: 220.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.primary.withValues(alpha: 0.1),
                          ),
                          child: FadeTransition(
                            opacity: _fadeAnimation,
                            child: Lottie.asset(Assets.images.otpVerification, fit: BoxFit.contain),
                          ),
                        ),
                        SizedBox(height: 32.h),
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Text(
                            'Email Verification',
                            style: TextStyles.extraBold.copyWith(
                              fontSize: 28.sp,
                              color: AppColors.black,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                        SizedBox(height: 12.h),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.w),
                          child: Text(
                            'Enter the OTP sent to your email',
                            style: TextStyles.regular.copyWith(
                              fontSize: 16.sp,
                              color: AppColors.black,
                              height: 1.5,
                            ),
                            maxLines: 2,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              ' ${controller.emailController.text}',
                              style: TextStyles.regular.copyWith(
                                fontSize: 16.sp,
                                color: AppColors.black,
                                height: 1.5,
                              ),
                              maxLines: 1,
                              textAlign: TextAlign.center,
                            ).paddingOnly(left: 10.w, right: 10.w),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  isEdit = !isEdit;
                                });
                              },
                              child: Icon(Icons.edit, color: AppColors.buttonColorDark),
                            ),
                          ],
                        ),

                        if (isEdit)
                          CommonInputFormField(
                            backgroundColor: AppColors.transparent,
                            borderColor: AppColors.black,
                            textEditingController: emailCtr,
                            validator: (Val) {
                              return null;
                            },
                            onChanged: (val) {},
                          ),
                        SizedBox(height: 40.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: List.generate(
                            emailOtpLength,
                            (index) => Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.black.withValues(alpha: 0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              width: 45.w,
                              height: 55.h,
                              child: TextFormField(
                                controller: controller.otpController[index],
                                keyboardType: TextInputType.number,
                                textAlign: TextAlign.center,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(1),
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                                style: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.black),
                                decoration: InputDecoration(
                                  counter: const SizedBox.shrink(),
                                  contentPadding: EdgeInsets.zero,
                                  filled: true,
                                  fillColor: AppColors.white,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.r),
                                    borderSide: BorderSide(color: AppColors.black),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12.r),
                                    borderSide: BorderSide(color: AppColors.primary, width: 2),
                                  ),
                                ),
                                onChanged: (value) {
                                  if (value.length == 1 && index < 3) {
                                    FocusScope.of(context).nextFocus();
                                  }
                                  if (value.isEmpty && index > 0) {
                                    FocusScope.of(context).previousFocus();
                                  }

                                  if (controller.getOtp().length == emailOtpLength) {
                                    hideKeyboard(context);
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 6),
                              ),
                            ],
                          ),
                          child: CommonButton(
                            buttonText: 'Verify OTP',
                            height: 52.h,
                            onTap: () async {
                              hideKeyboard(context);
                              await controller.verifyOtp(ref, context);

                              Future.delayed(Duration(milliseconds: 100), () async {
                                if (controller.verifyOTPState.success?.success ?? false) {
                                  Map<String, Object> map = {
                                    'full_name': controller.nameController.text.trim(),
                                    'date_of_birth': controller.dobController.text.trim(),
                                    'gender': (controller.selectedGender ?? '').toLowerCase(),
                                    'email': controller.emailController.text.trim().toLowerCase(),
                                  };

                                  /// Track Otp Verification event
                                  AnalyticsEvents.otpVerification.track(parameters: map);
                                  await controller.getUserProfile(ref);
                                } else {
                                  AppToast.showSnackBar(
                                    controller.verifyOTPState.success?.message ?? '',
                                    iconImage: Icons.error,
                                    iconColor: AppColors.redF94008,
                                    textColor: AppColors.redF94008,
                                    decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
                                    snackbarKey.currentState,
                                    () {
                                      snackbarKey.currentState?.hideCurrentSnackBar();
                                    },
                                  );
                                }
                              });
                            },
                          ),
                        ),

                        SizedBox(height: 20.h),
                        InkWell(
                          onTap: () {
                            /// TODO: Resend Api Call
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CommonText(
                                title: 'Don’t received OTP ? ',
                                textStyle: TextStyles.medium.copyWith(
                                  color: AppColors.textPrimary,
                                  fontSize: 15.sp,
                                ),
                              ),
                              CommonText(
                                title: 'Resend',
                                textStyle: TextStyles.medium.copyWith(
                                  color: AppColors.buttonColorDark,
                                  fontSize: 15.sp,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              DialogProgressBar(isLoading: controller.isLoading),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:dateme/framework/controller/cms/cms_controller.dart';
import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CmsScreen extends ConsumerStatefulWidget {
  final String cmsUrl;
  final String? appBarTitle;

  const CmsScreen({super.key, required this.cmsUrl, this.appBarTitle});

  @override
  ConsumerState<CmsScreen> createState() => _CmsMobileState();
}

class _CmsMobileState extends ConsumerState<CmsScreen> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final cmsWatch = ref.read(cmsController);
      cmsWatch.disposeController(isNotify: true);
      cmsWatch.initializeUrl(widget.cmsUrl);

      /// Track Visited Urls event
      AnalyticsEvents.cms.track(parameters: {'visited_url': widget.cmsUrl});
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final cmsWatch = ref.watch(cmsController);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (val, pop) async {
        try {
          if (await cmsWatch.webViewController?.canGoBack() ?? false) {
            await cmsWatch.webViewController?.goBack();
          } else {
            ref.read(navigationStackController).pop();
          }
        } catch (e) {
          showLog('Error $e');
        }
      },
      child: Scaffold(
        appBar: CommonAppBar(
          title: widget.appBarTitle ?? '',
          onBackPressed: () async {
            if (await cmsWatch.webViewController?.canGoBack() ?? false) {
              await cmsWatch.webViewController?.goBack();
            } else {
              ref.read(navigationStackController).pop();
            }
          },
          isShowBack: true,
        ),
        backgroundColor: AppColors.white,
        body: _bodyWidget(),
      ),
    );
  }

  ///Body Widget
  Widget _bodyWidget() {
    final cmsWatch = ref.watch(cmsController);
    return (cmsWatch.isLoading)
        ? Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LinearProgressIndicator(
              color: AppColors.primary,
              value: cmsWatch.loadingProgress.toDouble() / 100,
              backgroundColor: AppColors.buttonColor,
              minHeight: 2.h,
            ),
            Expanded(child: DialogProgressBar(isLoading: cmsWatch.isLoading)),
          ],
        )
        : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (cmsWatch.webViewController != null)
              Expanded(
                child: WebViewWidget(
                  controller: cmsWatch.webViewController!,
                ).paddingSymmetric(vertical: 15.h),
              ),
          ],
        );
  }
}

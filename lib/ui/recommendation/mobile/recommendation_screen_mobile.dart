import 'dart:async';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:dateme/framework/controller/connection/connection_controller.dart';
import 'package:dateme/framework/controller/recommendation/recommendation_screen_controller.dart';
import 'package:dateme/framework/controller/wallet/wallet_screen_controller.dart';
import 'package:dateme/framework/repository/recommendation/model/get_match_recommendation_list_response_model.dart';
import 'package:dateme/framework/utils/extension/context_extension.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/extension/string_extension.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/recommendation/helper/expandable_chat_buttons.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RecommendationScreenMobile extends ConsumerStatefulWidget {
  const RecommendationScreenMobile({super.key});

  @override
  ConsumerState<RecommendationScreenMobile> createState() => _RecommendationScreenMobileState();
}

class _RecommendationScreenMobileState extends ConsumerState<RecommendationScreenMobile> {
  /// CarouselSlider Controller
  CarouselSliderController carouselSliderController = CarouselSliderController();

  ///Init Override
  @override
  void initState() {
    super.initState();
    final recommendationScreenWatch = ref.read(recommendationScreenController);
    recommendationScreenWatch.cardSwiperController = CardSwiperController();

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      final connectionScreenWatch = ref.read(connectionController);
      final walletScreenWatch = ref.watch(walletController);
      connectionScreenWatch.disposeController(isNotify: true);
      walletScreenWatch.disposeController(isNotify: true);
      await walletScreenWatch.getWalletBalanceAPI();
      _loadRecommendations();
    });
  }

  /// Load recommendations with error handling
  Future<void> _loadRecommendations() async {
    try {
      final recommendationScreenWatch = ref.read(recommendationScreenController);
      recommendationScreenWatch.disposeController(isNotify: true);
      await recommendationScreenWatch.getMatchRecommendationsList(ref);
    } catch (e) {
      // Log error
      showLog('Error loading recommendations: $e');
      // Show error message if needed
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load recommendations. Please try again.'),
            backgroundColor: AppColors.red,
            action: SnackBarAction(
              label: 'Retry',
              textColor: AppColors.white,
              onPressed: _loadRecommendations,
            ),
          ),
        );
      }
    }
  }

  ///Dispose Override
  @override
  void dispose() {
    // final recommendationScreenWatch = ref.read(recommendationScreenController);
    // recommendationScreenWatch.cardSwiperController.dispose();
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final recommendationScreenWatch = ref.watch(recommendationScreenController);
    // final connectionScreenWatch = ref.watch(connectionController);

    return commonBackgroundDecoration(
      context,
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: AppColors.white,
            // appBar: CommonAppBar(
            //   title: '',
            //   isShowBack: false,
            //   bgColor: AppColors.transparent,
            //   actionWidgets: [
            //     InkWell(
            //       onTap: () {
            //         ref.read(navigationStackController).push(NavigationStackItem.walletScreen(isFrom: 'Recommendation'));
            //       },
            //       child: Container(
            //         padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            //         decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
            //         child: Row(
            //           mainAxisAlignment: MainAxisAlignment.center,
            //           children: [
            //             Icon(Icons.link, size: 16, color: AppColors.buttonColorDark),
            //             CommonText(
            //               title: walletScreenWatch.getWalletBalanceState.success?.balance != null ? ' ${walletScreenWatch.getWalletBalanceState.success?.balance}' : '0',
            //               textStyle: TextStyles.medium.copyWith(color: AppColors.buttonColorDark, fontSize: 16.sp),
            //             ).paddingOnly(left: 10.w),
            //           ],
            //         ),
            //       ).paddingOnly(right: 20.w),
            //     ),
            //   ],
            // ),
            body: StreamBuilder<Object>(
              stream: null,
              builder: (context, snapshot) {
                return commonBackgroundDecoration(
                  context,
                  child: SafeArea(child: _bodyWidget()),
                ).paddingOnly(bottom: 20.h);
              },
            ),
          ),
          // DialogProgressBar(isLoading: recommendationScreenWatch.getMatchRecommendationState.isLoading),
        ],
      ),
    );
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty<bool>('mounted', mounted));
  }

  ///Body Widget
  Widget _bodyWidget() {
    final recommendationScreenWatch = ref.read(recommendationScreenController);

    if (recommendationScreenWatch.getMatchRecommendationState.isLoading) {
      return Center(
        child: DialogProgressBar(isLoading: recommendationScreenWatch.getMatchRecommendationState.isLoading),
      );
    }

    if (recommendationScreenWatch.recommendationList.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // Card swiper takes most of the screen
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h).copyWith(bottom: 50.h),

            /// Extra space at bottom
            child: CardSwiper(
              controller: recommendationScreenWatch.cardSwiperController,
              cardsCount: recommendationScreenWatch.recommendationList.length,
              numberOfCardsDisplayed: recommendationScreenWatch.recommendationList.length > 1 ? 2 : 1,
              backCardOffset: const Offset(0, 40),
              padding: EdgeInsets.zero,
              allowedSwipeDirection: const AllowedSwipeDirection.only(left: true, right: true),
              onSwipe: (int previousIndex, int? targetIndex, CardSwiperDirection direction) async {
                final recommendation = recommendationScreenWatch.recommendationList[previousIndex];
                final userHashId = recommendation.userHashid;

                if (userHashId == null) return true;

                // Handle swipe direction
                if (direction == CardSwiperDirection.right || direction == CardSwiperDirection.top) {
                  // Like - right or top swipe
                  // await connectionScreenWatch.likeProfile(userHashId);
                } else if (direction == CardSwiperDirection.left || direction == CardSwiperDirection.bottom) {
                  // Dislike - left or bottom swipe
                  // await connectionScreenWatch.dislikeProfile(userHashId);
                }

                return true;
              },
              cardBuilder: (context, index, percentThresholdX, percentThresholdY) {
                final recommendation = recommendationScreenWatch.recommendationList[index];
                return _buildSwipeableCard(recommendation, percentThresholdX, percentThresholdY);
              },
            ),
          ),
        ),
      ],
    ).paddingOnly(bottom: 70.h);
  }

  /// Empty state when no recommendations are available
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person_search, size: 80.h, color: AppColors.primary),
          SizedBox(height: 16.h),
          CommonText(
            title: 'No recommendations found',
            textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 18.sp),
          ),
          SizedBox(height: 8.h),
          CommonText(
            title: "We're working on finding your perfect match",
            textStyle: TextStyles.regular.copyWith(color: AppColors.grey8A8A8A, fontSize: 14.sp),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          CommonButton(buttonText: 'Refresh', width: 150.w, onTap: _loadRecommendations),
        ],
      ).paddingSymmetric(horizontal: 24.w),
    );
  }

  /// Swipe able card for the card swiper
  Widget _buildSwipeableCard(
    RecommendationResult recommendation,
    int percentThresholdX,
    int percentThresholdY,
  ) {
    final walletScreenWatch = ref.watch(walletController);
    return Stack(
      children: [
        SizedBox(
          height: context.height,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(30.r),
            child: Container(
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(30.r), color: AppColors.white),
              child: _buildPhotoCarousel(recommendation),
            ),
          ),
        ),
        _buildProfileInfo(recommendation).alignAtBottomCenter(),
      ],
    );
  }

  /// Photo carousel for profile images
  Widget _buildPhotoCarousel(RecommendationResult recommendation) {
    final connectionWatch = ref.watch(connectionController);
    final recommendationScreenWatch = ref.read(recommendationScreenController);
    List<MediaList> mediaUrls = recommendation.mediaList ?? [];

    if (mediaUrls.isEmpty) {
      return Container(
        color: AppColors.greyF7F8F8,
        child: Center(child: Icon(Icons.person, size: 80.h, color: AppColors.grey9F9F9F)),
      );
    }

    final validMediaUrls =
        mediaUrls
            .where(
              (url) =>
                  url.mediaUrl!.isNotEmpty &&
                  (url.mediaUrl!.startsWith('http://') || url.mediaUrl!.startsWith('https://')),
            )
            .toList();

    if (validMediaUrls.isEmpty) {
      return Container(
        color: AppColors.greyF7F8F8,
        child: Center(child: Icon(Icons.person, size: 80.h, color: AppColors.grey9F9F9F)),
      );
    }

    String userHashId = recommendation.userHashid ?? 'unknown';
    if (!recommendationScreenWatch.imageIndices.containsKey(userHashId)) {
      recommendationScreenWatch.imageIndices[userHashId] = 0;
    }
    int currentImageIndex = recommendationScreenWatch.imageIndices[userHashId]!;

    return Stack(
      children: [
        CarouselSlider(
          carouselController: carouselSliderController,
          options: CarouselOptions(
            height: double.infinity,
            viewportFraction: 1.0,
            enlargeCenterPage: false,
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 3),
            enableInfiniteScroll: validMediaUrls.length > 1,
            onPageChanged: (index, reason) {
              recommendationScreenWatch.imageIndices[userHashId] = index;
              recommendationScreenWatch.updateWidget();
            },
          ),
          items:
              validMediaUrls.map((url) {
                return Builder(
                  builder: (BuildContext context) {
                    return InkWell(
                      onTap: () {
                        if (currentImageIndex < validMediaUrls.length - 1) {
                          recommendationScreenWatch.imageIndices[userHashId] = currentImageIndex + 1;
                          carouselSliderController.animateToPage(currentImageIndex + 1);
                          recommendationScreenWatch.updateWidget();
                        } else {
                          recommendationScreenWatch.imageIndices[userHashId] = 0;
                          carouselSliderController.animateToPage(0);
                          recommendationScreenWatch.updateWidget();
                        }
                      },
                      onDoubleTap: () async {
                        /// Like/Dislike Api Call
                        if (recommendation.isLiked ?? false) {
                          AnalyticsEvents.removeLiked.track(
                            parameters: {'userHashid': recommendation.userHashid ?? 'unknown'},
                          );
                          await connectionWatch.removeLikeAPI(recommendation.userHashid ?? '', false);
                        } else {
                          AnalyticsEvents.liked.track(
                            parameters: {'userHashid': recommendation.userHashid ?? 'unknown'},
                          );
                          await connectionWatch.createLikeAPI(recommendation.userHashid ?? '', false);
                        }

                        await Future.delayed(Duration(milliseconds: 100), () async {
                          if (connectionWatch.createLikeState.success?.likeHashid != null ||
                              connectionWatch.removeLikeState.success?.likeHashid != null) {
                            recommendation.isLiked = !(recommendation.isLiked ?? false);
                          }
                          connectionWatch.updateWidget();
                        });
                      },
                      child: CommonImage(
                        strIcon: url.mediaUrl!,
                        boxFit: BoxFit.cover,
                        height: context.height,
                        width: context.width,
                      ),
                    );
                  },
                );
              }).toList(),
        ),

        /// Timeline Progress Indicators
        if (validMediaUrls.length > 1)
          Positioned(
            top: 12.h,
            left: 12.w,
            right: 12.w,
            child: Row(
              children: List.generate(
                validMediaUrls.length,
                (index) => Expanded(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Inactive timeline (background)
                      Container(
                        height: 2.h,
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2.r),
                          color: AppColors.white.withAlpha(76),
                        ),
                      ),

                      // Active timeline
                      Container(
                        height: 2.h,
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        child:
                            index == currentImageIndex
                                ? TweenAnimationBuilder<double>(
                                  tween: Tween(begin: 0.0, end: 1.0),
                                  duration: const Duration(seconds: 3),
                                  builder: (context, value, child) {
                                    // final remainingSeconds = ((1 - value) * 3).round();
                                    return Stack(
                                      children: [
                                        /// Background track
                                        Container(
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(2.r),
                                            color: AppColors.white.withAlpha(100),
                                          ),
                                        ),
                                        // Animated progress bar
                                        Align(
                                          alignment: Alignment.centerLeft,
                                          child: FractionallySizedBox(
                                            widthFactor: value,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                color: AppColors.white,
                                                borderRadius: BorderRadius.circular(2.r),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                  onEnd: () {
                                    if (currentImageIndex < validMediaUrls.length - 1) {
                                      recommendationScreenWatch.imageIndices[userHashId] =
                                          currentImageIndex + 1;
                                      recommendationScreenWatch.updateWidget();
                                    }
                                  },
                                )
                                : Container(
                                  color: index < currentImageIndex ? AppColors.white : AppColors.transparent,
                                ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Profile information section

  Widget _buildProfileInfo(RecommendationResult recommendation) {
    // final connectionScreenWatch = ref.read(connectionController);
    // final walletScreenWatch = ref.read(walletController);
    final connectionWatch = ref.read(connectionController);
    return Container(
      height: context.height * 0.4,
      alignment: Alignment.bottomCenter,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.transparent,
            AppColors.black.withValues(alpha: 0.4),
            AppColors.black.withValues(alpha: 0.7),
            AppColors.black.withValues(alpha: 1),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(30.r),
          bottomRight: Radius.circular(30.r),
        ),
      ),
      padding: EdgeInsets.all(20.h),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                /// Name, age and verification badges
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () => _showFullProfile(recommendation),
                          child: SizedBox(
                            height: 30.h,
                            child: Row(
                              children: [
                                Text(
                                  "${recommendation.firstName?.capitalizeFirstLetterOfSentence ?? 'User'}, ${recommendation.age ?? ''}",
                                  style: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 24.sp),
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (recommendation.selfieVerified == true)
                          Icon(Icons.verified, color: AppColors.primary, size: 20.h).paddingOnly(left: 4.w),
                      ],
                    ),
                  ],
                ),

                /// Location and relationship intent
                if (recommendation.currentCity != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CommonText(
                        title: 'In ${recommendation.currentCity!}',
                        textStyle: TextStyles.light.copyWith(color: AppColors.white, fontSize: 18.sp),
                      ),
                    ],
                  ),
                SizedBox(height: 8.h),

                /// Relationship intent badge
                userHabits(recommendation),

                SizedBox(height: 12.h),

                /// Bottom action row
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    /// View full profile button
                    InkWell(
                      onTap: () => _showFullProfile(recommendation),
                      child: CommonImage(
                        strIcon: Assets.svgs.svgInfo,
                        height: 25.h,
                        width: 25.h,
                        boxFit: BoxFit.fitWidth,
                      ),
                    ),
                    SizedBox(width: 12.w),

                    /// Compatibility score with animation
                    // if (recommendation.compatibilityScore != null)
                    //   TweenAnimationBuilder<double>(
                    //     tween: Tween(begin: 0.0, end: recommendation.compatibilityScore!),
                    //     duration: const Duration(seconds: 1),
                    //     builder:
                    //         (context, value, _) => Container(
                    //           padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    //           decoration: BoxDecoration(
                    //             color: AppColors.white.withValues(alpha: 0.15),
                    //             borderRadius: BorderRadius.circular(20.r),
                    //           ),
                    //           child: Row(
                    //             mainAxisSize: MainAxisSize.min,
                    //             children: [
                    //               Icon(Icons.favorite, color: AppColors.red, size: 16.h),
                    //               SizedBox(width: 4.w),
                    //               Text(
                    //                 '${(value * 100).toInt()}%',
                    //                 style: TextStyles.semiBold.copyWith(
                    //                   color: AppColors.white,
                    //                   fontSize: 14.sp,
                    //                 ),
                    //               ),
                    //             ],
                    //           ),
                    //         ),
                    //   ),

                    /// For Selected svgLike/svgLikeEmpty
                    InkWell(
                      onTap: () async {
                        showLog('recommendation.isLiked ${recommendation.isLiked}');

                        /// Like/Dislike Api Call
                        if (recommendation.isLiked ?? false) {
                          AnalyticsEvents.removeLiked.track(
                            parameters: {'userHashid': recommendation.userHashid ?? 'unknown'},
                          );
                          await connectionWatch.removeLikeAPI(recommendation.userHashid ?? '', false);
                        } else {
                          AnalyticsEvents.liked.track(
                            parameters: {'userHashid': recommendation.userHashid ?? 'unknown'},
                          );
                          await connectionWatch.createLikeAPI(recommendation.userHashid ?? '', false);
                        }

                        await Future.delayed(Duration(milliseconds: 100), () async {
                          if (connectionWatch.createLikeState.success?.likeHashid != null ||
                              connectionWatch.removeLikeState.success?.likeHashid != null) {
                            recommendation.isLiked = !(recommendation.isLiked ?? false);
                          }
                          connectionWatch.updateWidget();
                        });
                      },
                      child:
                          (connectionWatch.createLikeState.isLoading ||
                                  connectionWatch.removeLikeState.isLoading)
                              ? CircularProgressIndicator()
                              : CommonImage(
                                strIcon:
                                    (recommendation.isLiked ?? false)
                                        ? Assets.svgs.svgLike
                                        : Assets.svgs.svgLikeEmpty,
                                height: 25.h,
                                width: 25.h,
                                boxFit: BoxFit.fitWidth,
                              ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: 20.w),

          /// Chat Buttons
          ExpandableChatButtons(recommendation: recommendation),
        ],
      ),
    );
  }

  /// Full profile modal
  void _showFullProfile(RecommendationResult profile) {
    AnalyticsEvents.otherProfileView.track(parameters: {'userHashid': profile.userHashid ?? 'unknown'});
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.9,
            minChildSize: 0.5,
            maxChildSize: 0.9,
            builder:
                (_, controller) => Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
                    child: SingleChildScrollView(
                      controller: controller,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Profile header with images carousel
                          CarouselSlider(
                            options: CarouselOptions(
                              height: 300.h,
                              viewportFraction: 1,
                              enableInfiniteScroll: (profile.mediaList?.length ?? 0) > 1,
                            ),
                            items:
                                (profile.mediaList ?? [])
                                    .map(
                                      (url) => CommonImage(
                                        strIcon: url.mediaUrl ?? '',
                                        width: double.infinity,
                                        height: 300.h,
                                        boxFit: BoxFit.cover,
                                      ),
                                    )
                                    .toList(),
                          ),

                          // Profile details sections
                          Padding(
                            padding: EdgeInsets.all(16.h),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildProfileSection(
                                  'About',
                                  children: [
                                    Text(
                                      "${profile.firstName ?? ''}, ${profile.age ?? ''}",
                                      style: TextStyles.regular.copyWith(fontSize: 14.sp),
                                    ),
                                  ],
                                ),
                                _buildProfileSection(
                                  'Bio',
                                  children: [
                                    if (profile.bio?.isNotEmpty == true)
                                      Text(profile.bio!, style: TextStyles.regular.copyWith(fontSize: 14.sp)),
                                  ],
                                ),

                                _buildProfileSection(
                                  'Basic Info',
                                  children: [
                                    _buildInfoRow('Age', '${profile.age ?? ""}'),
                                    _buildInfoRow('Location', profile.currentCity ?? ''),
                                    _buildInfoRow(
                                      'Looking for',
                                      profile.relationshipIntent?.replaceAll('_', ' ') ?? '',
                                    ),
                                  ],
                                ),
                                if (profile.interests?.isNotEmpty == true)
                                  _buildProfileSection(
                                    'Interests',
                                    children: [
                                      Wrap(
                                        spacing: 8.w,
                                        runSpacing: 8.h,
                                        children:
                                            profile.interests!
                                                .map(
                                                  (interest) => Container(
                                                    padding: EdgeInsets.symmetric(
                                                      horizontal: 12.w,
                                                      vertical: 6.h,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      color: AppColors.primary.withValues(alpha: 0.1),
                                                      borderRadius: BorderRadius.circular(20.r),
                                                    ),
                                                    child: Text(
                                                      interest,
                                                      style: TextStyles.medium.copyWith(fontSize: 12.sp),
                                                    ),
                                                  ),
                                                )
                                                .toList(),
                                      ),
                                    ],
                                  ),
                                // Add more sections as needed
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
          ),
    );
  }

  ///
  Widget _buildProfileSection(String title, {required List<Widget> children}) {
    if (children.isEmpty) return SizedBox.shrink();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyles.semiBold.copyWith(fontSize: 18.sp)),
        SizedBox(height: 12.h),
        ...children,
        SizedBox(height: 24.h),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    if (value.isEmpty) return SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Text('$label: ', style: TextStyles.medium.copyWith(color: AppColors.grey8A8A8A, fontSize: 14.sp)),
          Text(value, style: TextStyles.regular.copyWith(fontSize: 14.sp)),
        ],
      ),
    );
  }

  /// User Habits Tab
  Widget userHabits(RecommendationResult profile) {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: [
        if (profile.relationshipIntent != null)
          commonInterestDecoration(
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, color: AppColors.white, size: 16.h),
                SizedBox(width: 4.w),
                Flexible(
                  child: Text(
                    profile.relationshipIntent!.replaceAll('_', ' '),
                    style: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 14.sp),
                  ),
                ),
              ],
            ),
          ),

        ...?profile.interests?.map(
          (interest) => commonInterestDecoration(
            Text(interest, style: TextStyles.regular.copyWith(color: AppColors.white, fontSize: 14.sp)),
          ),
        ),
      ],
    );
  }

  ///
  commonInterestDecoration(Widget child) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(120),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: child,
    );
  }
}

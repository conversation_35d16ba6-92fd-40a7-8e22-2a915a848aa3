import 'package:dateme/framework/controller/profile_onboarding/live_photo_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/gen/assets.gen.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_dialogs.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class IdentityVerificationScreen extends ConsumerWidget {
  const IdentityVerificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final livePhotoControllerWatch = ref.watch(livePhotoControllerProvider);
    return commonBackgroundDecoration(
      context,
      child: SafeArea(
        top: false,
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildVerificationCard(
                  context,
                  image:
                      (ref.read(livePhotoControllerProvider).profilePhoto() != null &&
                              ref.read(livePhotoControllerProvider).profilePhoto()?.mediaUrl != '')
                          ? (ref.read(livePhotoControllerProvider).profilePhoto()?.mediaUrl ?? '')
                          : Assets.svgs.svgCamera,
                  image2: Assets.svgs.svgCameraGrey,
                  title: 'Take a photo',
                  subtitle: 'Upload',
                  description: 'Take a photo with your front camera to verify your identity',
                  onTap: () {
                    // if (ref.read(livePhotoControllerProvider).profilePhoto() != null &&
                    //     ref.read(livePhotoControllerProvider).profilePhoto()?.mediaUrl != '') {
                    //   showMessageDialog(context, 'Selfie already uploaded', () {});
                    // } else {
                    ref.read(navigationStackController).push(NavigationStackItem.liveSelfie());
                    // }
                  },
                  isSuccess: ref.read(livePhotoControllerProvider).getPhotosStatus(isNotify: false),
                ),
                const SizedBox(height: 20),
                _buildVerificationCard(
                  context,
                  image:
                      livePhotoControllerWatch.getAadhaarStatus()
                          ? Assets.images.icAadharPlaceholder.path
                          : Assets.svgs.svgId,
                  image2: Assets.svgs.svgIdGrey,
                  title: 'Aadhaar Number',
                  subtitle: 'Submit',
                  description: 'Enter your Aadhaar number to verify your identity',
                  onTap: () {
                    ref.read(navigationStackController).push(NavigationStackItem.aadhaarVerification());
                  },
                  isSuccess: livePhotoControllerWatch.getAadhaarStatus(),
                ),
                SizedBox(height: 100.h),
              ],
            ),
          ),
          bottomNavigationBar: CommonButton(
            buttonText: 'Next',
            onTap: () {
              /// TODO: Check Aadhaar status and profile photo before proceeding
              if ( /*livePhotoControllerWatch.getAadhaarStatus() &&*/ ref
                      .read(livePhotoControllerProvider)
                      .profilePhoto()
                      ?.mediaUrl != '') {
                ref.read(navigationStackController).pushAndRemoveAll(NavigationStackItem.basicProfile());
              } else {
                showMessageDialog(context, 'Please complete the verification steps', () {});
                return;
              }
            },
          ).paddingAll(12.sp),
        ),
      ),
    );
  }

  Widget _buildVerificationCard(
    BuildContext context, {
    required String image,
    required String image2,
    required String title,
    required String subtitle,
    required String description,
    required VoidCallback onTap,
    required bool isSuccess,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          /// Card View
          Container(
            margin: EdgeInsets.only(top: 35.h),
            decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(24.r)),
            child: Card(
              child: Column(
                children: [
                  SizedBox(height: 30.h),
                  CommonText(
                    title: description,
                    textAlign: TextAlign.center,
                    maxLines: 5,
                    textStyle: TextStyles.light.copyWith(fontSize: 14, color: AppColors.black),
                  ).paddingSymmetric(horizontal: 30.w),
                  SizedBox(height: 20.h),
                  Divider(color: AppColors.greyBCBCBC, thickness: 1, height: 0.5),
                  SizedBox(height: 20.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(width: 30.w),
                      CommonImage(strIcon: image2, height: 50.h, width: 50.h),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonText(
                            title: title,
                            textStyle: TextStyles.bold.copyWith(fontSize: 16.sp, color: AppColors.black),
                          ),
                          CommonText(
                            title: subtitle,
                            textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.black),
                          ),
                        ],
                      ),
                      const Spacer(),
                      isSuccess
                          ? Icon(
                            Icons.check_circle,
                            size: 30.h,
                            color: AppColors.green,
                          ).paddingOnly(right: 30.w)
                          : const Offstage(),
                    ],
                  ),
                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ),

          /// Image
          Align(
            alignment: Alignment.topCenter,
            child: CommonImage(
              strIcon: image,
              height: 60.h,
              width: 60.h,
              boxFit: BoxFit.cover,
              bottomLeftRadius: isSuccess ? 100.r : 12.r,
              bottomRightRadius: isSuccess ? 100.r : 12.r,
              topLeftRadius: isSuccess ? 100.r : 12.r,
              topRightRadius: isSuccess ? 100.r : 12.r,
            ),
          ),
        ],
      ),
    ).paddingSymmetric(horizontal: 30.w);
  }
}

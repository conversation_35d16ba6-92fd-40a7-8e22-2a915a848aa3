import 'package:dateme/framework/controller/profile_onboarding/identity_verification/mobile_verification_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MobileVerificationScreen extends ConsumerStatefulWidget {
  const MobileVerificationScreen({super.key});

  @override
  ConsumerState<MobileVerificationScreen> createState() => _MobileVerificationScreenState();
}

class _MobileVerificationScreenState extends ConsumerState<MobileVerificationScreen> {
  bool isEdit = false;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      ref.read(mobileVerificationController).disposeController(isNotify: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final mobileWatch = ref.watch(mobileVerificationController);

    return commonBackgroundDecoration(
      context,
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.transparent,
        body: Stack(
          children: [
            SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 30.w),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 30.h),
                        if (!mobileWatch.isPhoneVerified)
                          _buildPhoneInput(mobileWatch)
                        else
                          _buildOTPInput(mobileWatch),
                        // const Spacer(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            DialogProgressBar(isLoading: mobileWatch.sendOTPState.isLoading),
          ],
        ),
        bottomNavigationBar: SafeArea(child: _buildActionButton(mobileWatch)),
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Row(
      children: [
        CommonText(
          title: title,
          maxLines: 2,
          textStyle: TextStyles.semiBold.copyWith(fontSize: 35.sp, color: AppColors.black),
        ),
      ],
    );
  }

  Widget _buildPhoneInput(MobileVerificationController mobileWatch) {
    return Form(
      key: mobileWatch.formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildHeader('Phone number\nVerification,'),
          SizedBox(height: 80.h),
          CommonText(
            title: 'Enter your phone number',
            textStyle: TextStyles.semiBold.copyWith(fontSize: 25.sp, color: AppColors.black),
          ),
          CommonText(
            title: "We'll send you a verification code to ensure it's really you",
            maxLines: 2,
            textStyle: TextStyles.light.copyWith(fontSize: 14.sp, color: AppColors.black),
          ).paddingSymmetric(horizontal: 30.w),

          SizedBox(height: 25.h),
          CommonInputFormField(
            prefixWidget: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),

                  child: CommonText(
                    title: '+91',
                    textStyle: TextStyles.semiBold.copyWith(fontSize: 16.sp, color: AppColors.black),
                  ),
                ),
              ],
            ),
            textEditingController: mobileWatch.phoneController,
            hintText: 'Enter mobile number',
            textInputType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            maxLength: 10,
            validator: mobileWatch.validatePhone,
            // errorText: mobileWatch.phoneError,
          ),
        ],
      ),
    );
  }

  Widget _buildOTPInput(MobileVerificationController mobileWatch) {
    return Form(
      key: mobileWatch.formKey1,
      child: Column(
        children: [
          _buildHeader('Phone number\nVerification,'),
          SizedBox(height: 80.h),
          CommonText(
            title: 'Otp Verification',
            textStyle: TextStyles.semiBold.copyWith(fontSize: 25.sp, color: AppColors.black),
          ),
          CommonText(
            title: 'Enter the OTP sent to:',
            maxLines: 2,
            textStyle: TextStyles.light.copyWith(fontSize: 14.sp, color: AppColors.black),
          ).paddingSymmetric(horizontal: 30.w),

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                ' ${mobileWatch.phoneController.text}',
                style: TextStyles.regular.copyWith(fontSize: 16.sp, color: AppColors.black, height: 1.5),
                maxLines: 1,
                textAlign: TextAlign.center,
              ).paddingOnly(left: 10.w, right: 10.w),
              InkWell(
                onTap: () {
                  setState(() {
                    isEdit = !isEdit;
                    if (isEdit == true) {
                      mobileWatch.isPhoneVerified = false;
                      isEdit = false;
                    }
                  });
                },
                child: Icon(Icons.edit, color: AppColors.buttonColorDark),
              ),
            ],
          ),

          SizedBox(height: 25.h),

          /// Otp View
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              mobileOtpLength,
              (index) => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                width: 45.w,
                height: 55.h,
                child: TextFormField(
                  controller: mobileWatch.otpController[index],
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(1),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  style: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.black),
                  decoration: InputDecoration(
                    counter: const SizedBox.shrink(),
                    contentPadding: EdgeInsets.zero,
                    filled: true,
                    fillColor: AppColors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.black),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                  ),
                  // validator: (v) {
                  //   return null;
                  // },
                  onChanged: (value) {
                    if (value.length == 1 && index < mobileOtpLength - 1) {
                      FocusScope.of(context).nextFocus();
                    }
                    if (value.isEmpty && index > 0) {
                      FocusScope.of(context).previousFocus();
                    }

                    if (mobileWatch.getOtp().length == 6) {
                      hideKeyboard(context);
                    }
                  },
                ),
              ),
            ),
          ),
          if (mobileWatch.otpError != null)
            CommonText(
              title: mobileWatch.otpError!,
              textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.red),
            ).paddingSymmetric(horizontal: 20.w, vertical: 20.h).alignAtBottomLeft(),

          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CommonText(
                title: 'Didn\'t receive code?',
                textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.black),
              ),
              if (mobileWatch.isResendEnabled)
                TextButton(
                  onPressed: () async {
                    for (var mobileWatch in mobileWatch.otpController) {
                      mobileWatch.clear();
                    }

                    if (!(mobileWatch.formKey.currentState?.validate() ?? false)) return;
                    await mobileWatch.sendOTPMobile(context);

                    await Future.delayed(Duration(milliseconds: 100), () {
                      if (mobileWatch.sendOTPState.success?.status ?? false) {
                        mobileWatch.isPhoneVerified = true;
                        mobileWatch.startResendTimer();
                        mobileWatch.updateWidget();
                      }
                    });
                  },
                  child: CommonText(
                    title: 'Resend',
                    textStyle: TextStyles.semiBold.copyWith(
                      fontSize: 14.sp,
                      color: AppColors.buttonColorDark,
                    ),
                  ),
                )
              else
                TextButton(
                  onPressed: () {},
                  child: CommonText(
                    title: ' Resend in ${mobileWatch.resendTimer}s',
                    textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.buttonColorDark),
                  ),
                ),
            ],
          ).paddingSymmetric(horizontal: 20.w),
        ],
      ),
    );
  }

  Widget _buildActionButton(MobileVerificationController mobileWatch) {
    return CommonButton(
      onTap: () async {
        if (mobileWatch.isPhoneVerified) {
          mobileWatch.validateOTP(mobileWatch.getOtp());
          if (!(mobileWatch.formKey1.currentState?.validate() ?? false)) return;
          if (mobileWatch.otpError == null) {
            await mobileWatch.verifyOTPMobile(context);

            await Future.delayed(Duration(milliseconds: 100), () {
              if (mobileWatch.verifyOTPState.success?.status != null) {
                ref
                    .read(navigationStackController)
                    .pushAndRemoveAll(NavigationStackItem.identityVerification());
              }
            });
          }
        } else {
          for (var mobileWatch in mobileWatch.otpController) {
            mobileWatch.clear();
          }

          if (!(mobileWatch.formKey.currentState?.validate() ?? false)) return;
          await mobileWatch.sendOTPMobile(context);
          await Future.delayed(Duration(milliseconds: 100), () {
            if (mobileWatch.sendOTPState.success?.status ?? false) {
              mobileWatch.isPhoneVerified = true;
              mobileWatch.startResendTimer();
              mobileWatch.updateWidget();
            }
          });
        }
        mobileWatch.updateWidget();
      },
      showLoader: mobileWatch.sendOTPState.isLoading || mobileWatch.verifyOTPState.isLoading,
      buttonText: mobileWatch.isPhoneVerified ? 'Verify OTP' : 'Send OTP',
    ).paddingOnly(left: 20.w, right: 20.w, bottom: 10.h);
  }
}

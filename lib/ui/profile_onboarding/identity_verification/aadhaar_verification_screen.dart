import 'package:dateme/framework/controller/profile_onboarding/identity_verification/aadhaar_verification_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// AadhaarVerificationScreen
/// This screen allows the user to input their Aadhaar card number.
class AadhaarVerificationScreen extends ConsumerStatefulWidget {
  const AadhaarVerificationScreen({super.key});

  @override
  ConsumerState<AadhaarVerificationScreen> createState() => _AadhaarVerificationScreenState();
}

class _AadhaarVerificationScreenState extends ConsumerState<AadhaarVerificationScreen> {

  bool isEdit = false;
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final controller = ref.watch(aadhaarInputControllerProvider);
      controller.disposeController(isNotify: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(aadhaarInputControllerProvider);

    return commonBackgroundDecoration(
      context,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          appBar: CommonAppBar(title: '', isShowBack: true),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Form(
              key: controller.formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(LocaleKeys.keyVerifyingYourAadhaarNumber),
                  SizedBox(height: 80.h),
                  CommonText(
                    title: LocaleKeys.keyEnterYourAadhaarNumber,
                    textStyle: TextStyles.semiBold.copyWith(fontSize: 25.sp, color: AppColors.black),
                  ),
                  CommonText(
                    title: LocaleKeys.keyWeWillSendYouVerificationCodeOnYourRegisterMobileNumber,
                    maxLines: 2,
                    textStyle: TextStyles.light.copyWith(fontSize: 14.sp, color: AppColors.black),
                  ).paddingSymmetric(horizontal: 30.w),

                  SizedBox(height: 25.h),
                  controller.isAadhaarValidate
                      ? _buildOTPInput(controller)
                      : CommonInputFormField(
                        textEditingController: controller.aadhaarController,
                        hintText: '1234 5678 9012',
                        maxLength: 12,
                        textInputType: TextInputType.number,
                        onChanged: (value) {},
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return LocaleKeys.keyAadhaarNumberIsRequired;
                          } else if (value.length != 12 || !RegExp(r'^\d{12}$').hasMatch(value)) {
                            return LocaleKeys.keyEnterAValidTwelveDigitAadhaarNumber;
                          }
                          return null;
                        },
                        textInputFormatter: [FilteringTextInputFormatter.digitsOnly],
                      ),
                ],
              ),
            ),
          ),
          bottomNavigationBar: SafeArea(
            child: CommonButton(
              buttonText: LocaleKeys.keySubmit,
              onTap: () {
                controller.isAadhaarValidate ? controller.verifyOTP(ref) : controller.sendOTP();
              },
              showLoader: controller.isLoading,
            ).paddingSymmetric(horizontal: 12.w, vertical: 30.h),
          ),
        ),
      ),
    );
  }

  /// Otp View
  Widget _buildOTPInput(AadhaarVerificationController controller) {
    return Form(
      key: controller.formKey1,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                ' ${controller.aadhaarController.text}',
                style: TextStyles.regular.copyWith(fontSize: 16.sp, color: AppColors.black, height: 1.5),
                maxLines: 1,
                textAlign: TextAlign.center,
              ).paddingOnly(left: 10.w, right: 10.w),
              InkWell(
                onTap: () {
                  setState(() {
                    isEdit = !isEdit;
                    if (isEdit == true) {
                      controller.isAadhaarValidate = false;
                      isEdit = false;
                    }
                  });
                },
                child: Icon(Icons.edit, color: AppColors.buttonColorDark),
              ),
            ],
          ),

          SizedBox(height: 25.h),
          /// Otp View
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              aadhaarOtpLength,
              (index) => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                width: 45.w,
                height: 55.h,
                child: TextFormField(
                  controller: controller.otpController[index],
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(1),
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  style: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.black),
                  decoration: InputDecoration(
                    counter: const SizedBox.shrink(),
                    contentPadding: EdgeInsets.zero,
                    filled: true,
                    fillColor: AppColors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.black),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                  ),
                  validator: (v) {
                    return controller.validateOTP(v);
                  },
                  onChanged: (value) {
                    if (value.length == 1 && index < 6) {
                      FocusScope.of(context).nextFocus();
                    }
                    if (value.isEmpty && index > 0) {
                      FocusScope.of(context).previousFocus();
                    }
                  },
                ),
              ),
            ),
          ),

          if (controller.otpError != null)
            CommonText(
              title: controller.otpError!,
              textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.red),
            ).paddingSymmetric(horizontal: 20.w, vertical: 20.h).alignAtBottomLeft(),

          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CommonText(
                title: LocaleKeys.keyDidntReceivedCode,
                textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.black),
              ),
              if (controller.isResendEnabled)
                TextButton(
                  onPressed: () {
                    controller.resendTimer = 60;
                    controller.isResendEnabled = false;
                    controller.startResendTimer();
                    controller.sendOTP();
                  },
                  child: CommonText(
                    title: LocaleKeys.keyResend,
                    textStyle: TextStyles.semiBold.copyWith(
                      fontSize: 14.sp,
                      color: AppColors.buttonColorDark,
                    ),
                  ),
                )
              else
                TextButton(
                  onPressed: () {},
                  child: CommonText(
                    title: ' ${LocaleKeys.keyResendIn} ${controller.resendTimer}s',
                    textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.buttonColorDark),
                  ),
                ),
            ],
          ).paddingSymmetric(horizontal: 20.w),
        ],
      ),
    );
  }

  Widget _buildHeader(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CommonText(
          title: title,
          maxLines: 2,
          textStyle: TextStyles.semiBold.copyWith(fontSize: 35.sp, color: AppColors.black),
        ),
      ],
    );
  }
}

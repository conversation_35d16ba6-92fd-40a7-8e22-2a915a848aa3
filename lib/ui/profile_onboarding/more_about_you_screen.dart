import 'package:dateme/framework/controller/profile_onboarding/more_about_you_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/const/form_validations.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_dropdown.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MoreAboutYouScreen extends ConsumerStatefulWidget {
  const MoreAboutYouScreen({super.key});

  @override
  ConsumerState<MoreAboutYouScreen> createState() => _MoreAboutYouScreenState();
}

class _MoreAboutYouScreenState extends ConsumerState<MoreAboutYouScreen> {
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      final controller = ref.watch(moreAboutYourController);
      controller.disposeController();
      controller.interestListApi(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(moreAboutYourController);
    final profileWatch = ref.watch(profileOnboardingController);

    return commonBackgroundDecoration(
      context,
      child: SafeArea(
        top: false,
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          appBar: CommonAppBar(title: 'More About you', isShowBack: false, isCenterTitle: true),
          body: Form(
            key: controller.formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _topHeader('First impressions matter!'),
                  _topHeaderSubtitle('Tell us more so we can help you find someone who gets you..'),
                  _buildSectionTitle('Short Bio'),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CommonInputFormField(
                        textEditingController: controller.bioController,
                        hintText: 'Write something that reflects your personality',
                        maxLines: 2,
                        maxLength: maxBioLength,
                        borderRadius: BorderRadius.circular(22.r),
                        validator: controller.validateBio,
                        onChanged: (val) {
                          setState(() {});
                        },
                      ),
                      if (controller.bioController.text.isNotEmpty)
                        ValueListenableBuilder<TextEditingValue>(
                          valueListenable: controller.bioController,
                          builder: (context, value, child) {
                            return Padding(
                              padding: EdgeInsets.only(top: 4.h, left: 8.w),
                              child: Text('${value.text.length} / $maxBioLength', style: TextStyle(fontSize: 12.sp, color: AppColors.black0E0E0E)),
                            );
                          },
                        ),
                    ],
                  ),
                  _buildSectionTitle('Interests'),
                  _buildInterestMenu(controller),
                  if (controller.selectedInterests.contains('Other'))
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: CommonInputFormField(
                        textEditingController: controller.otherInterestController,
                        hintText: 'Specify Other Interest',
                        validator: (value) {
                          if (controller.selectedInterests.contains('Other') && (value == null || value.isEmpty)) {
                            return 'Please specify your interest';
                          }
                          return null;
                        },
                      ),
                    ),
                  const SizedBox(height: 20),
                  // Replace the Language Dropdown section with this code
                  _buildSectionTitle('Languages Spoken'),
                  _buildLanguageSelector(controller),
                  // _buildSelectedLanguages(controller),
                  if (controller.showOtherLanguageField)
                    Padding(
                      padding: EdgeInsets.only(top: 8.h),
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonInputFormField(
                              textEditingController: controller.otherLanguageController,
                              hintText: 'Add Custom Language',
                              onSubmit: (value) {
                                if (value.isNotEmpty) {
                                  controller.addCustomLanguage(value);
                                }
                              },
                              validator: (String? val) {
                                return null;
                              },
                            ),
                          ),
                          SizedBox(width: 8.w),
                          IconButton(
                            onPressed: () {
                              if (controller.otherLanguageController.text.isNotEmpty) {
                                controller.addCustomLanguage(controller.otherLanguageController.text);
                              }
                            },
                            icon: Icon(Icons.add_circle, color: AppColors.buttonColor, size: 32.w),
                          ),
                        ],
                      ),
                    ),

                  _buildSectionTitle('Height'),
                  _buildHeightPicker(controller),
                  const SizedBox(height: 20),
                  _buildSectionTitle('Diet'),
                  CommonDropdown(value: controller.selectedDiet, items: controller.dietOptions, onChanged: controller.updateDiet, hint: 'Select your diet'),
                  _buildSectionTitle('Smoking'),
                  CommonDropdown(value: controller.selectedSmoking, items: controller.smokingOptions, onChanged: controller.updateSmoking, hint: 'Select your smoking preference'),
                  _buildSectionTitle('Drinking'),
                  CommonDropdown(value: controller.selectedDrinking, items: controller.drinkingOptions, onChanged: controller.updateDrinking, hint: 'Select your drinking preference'),
                  _buildSectionTitle('Religion (Optional)'),
                  CommonDropdown(value: controller.selectedReligion, items: controller.religionOptions, onChanged: controller.updateReligion, hint: 'Select your religion'),
                  _buildSectionTitle('Caste (Optional)'),
                  CommonDropdown(value: controller.selectedCaste, items: controller.casteOptions, onChanged: controller.updateCaste, hint: 'Select your caste'),
                  _buildSectionTitle('Fitness Level'),
                  CommonDropdown(
                    value: controller.selectedFitnessLevel,
                    items: controller.fitnessOptions,
                    onChanged: controller.updateFitnessLevel,
                    hint: 'Select your fitness level',
                    validator: (String? val) {
                      return validateText(val, 'Please select your fitness level');
                    },
                  ),
                  _buildSectionTitle('Education Level'),
                  CommonDropdown(
                    value: controller.selectedEducation,
                    items: controller.educationLevels,
                    onChanged: controller.updateEducation,
                    hint: 'Select your education level',
                    validator: controller.validateEducation,
                  ),
                  _buildSectionTitle('Occupation'),
                  CommonDropdown(
                    value: controller.selectedOccupation,
                    items: controller.occupationOptions,
                    onChanged: controller.updateOccupation,
                    hint: 'Select your occupation',
                    validator: controller.validateOccupation,
                  ),
                  // CommonInputFormField(
                  //   textEditingController: controller.occupationController,
                  //   hintText: 'Enter your occupation',
                  //   validator: controller.validateOccupation,
                  // ),
                  _buildSectionTitle('Company Name (Optional)'),
                  CommonInputFormField(
                    textEditingController: controller.companyController,
                    hintText: 'Enter your company name',
                    validator: (String? val) {
                      return null;
                    },
                  ),
                  _buildSectionTitle('Income Range (Optional)'),
                  CommonDropdown(value: controller.selectedIncomeRange, items: controller.incomeRanges, onChanged: controller.updateIncomeRange, hint: 'Select your income range'),
                  const SizedBox(height: 20),
                  CommonButton(
                    buttonText: 'Save Details',
                    showLoader: profileWatch.updateProfileState.isLoading,
                    onTap: () async {
                      await saveDetails(controller);
                    },
                  ).paddingOnly(bottom: 40.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(padding: EdgeInsets.only(top: 20.h, bottom: 8.h), child: CommonText(title: title, textStyle: TextStyles.bold.copyWith(fontSize: 18.sp, color: AppColors.black)));
  }

  Widget _topHeader(String title) {
    return Padding(
      padding: EdgeInsets.only(top: 20.h, bottom: 8.h),
      child: CommonText(title: title, maxLines: 2, textStyle: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.topHeadingColor)),
    );
  }

  Widget _topHeaderSubtitle(String title) {
    return Padding(padding: EdgeInsets.only(bottom: 8.h), child: CommonText(title: title, maxLines: 10, textStyle: TextStyles.medium.copyWith(fontSize: 18.sp, color: AppColors.black)));
  }

  Widget _buildHeightPicker(MoreAboutYouController controller) {
    return Card(
      child: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(30.r), color: AppColors.white),
        child: Column(
          children: [
            SizedBox(
              height: 150.h,
              child: Row(
                children: [
                  Expanded(
                    child: CupertinoPicker(
                      scrollController: FixedExtentScrollController(initialItem: controller.selectedHeightFeet - 4),
                      itemExtent: 40,
                      onSelectedItemChanged: (index) => controller.updateHeightFeet(4 + index),
                      children: List.generate(5, (index) => Center(child: Text('${4 + index} ft'))),
                    ),
                  ),
                  Expanded(
                    child: CupertinoPicker(
                      scrollController: FixedExtentScrollController(initialItem: controller.selectedHeightInches),
                      itemExtent: 40,
                      onSelectedItemChanged: (index) => controller.updateHeightInches(index),
                      children: List.generate(12, (index) => Center(child: Text('$index in'))),
                    ),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 16.h),
                  child: CommonText(
                    title: 'Selected Height: ${controller.selectedHeightFeet}\'${controller.selectedHeightInches}"',
                    textAlign: TextAlign.center,
                    textStyle: TextStyles.medium.copyWith(fontSize: 16.sp, fontWeight: FontWeight.bold, color: AppColors.black),
                  ),
                ),
              ],
            ).paddingOnly(bottom: 10.h),
          ],
        ),
      ),
    );
  }

  // Add these new widget methods
  Widget _buildLanguageSelector(MoreAboutYouController controller) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r)),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children:
            controller.languageOptions.map((language) {
              final isSelected = controller.selectedLanguages.contains(language);
              return FilterChip(
                label: Text(language),
                selected: isSelected,
                showCheckmark: language != 'Other',
                labelStyle: TextStyles.medium.copyWith(color: isSelected ? AppColors.white : AppColors.primary, fontSize: 14.sp),
                backgroundColor: AppColors.white,
                checkmarkColor: isSelected ? AppColors.white : AppColors.primary,
                selectedColor: AppColors.buttonColor,
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
                side: BorderSide(color: AppColors.primary),
                onSelected: (selected) {
                  if (selected) {
                    controller.addLanguage(language);
                  } else {
                    controller.removeLanguage(language);
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildSelectedLanguages(MoreAboutYouController controller) {
    if (controller.selectedLanguages.isEmpty) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(color: AppColors.buttonColor.withAlpha(26), borderRadius: BorderRadius.circular(20.r)),
      child: Wrap(
        spacing: 8.w,
        runSpacing: 8.h,
        children:
            controller.selectedLanguages.where((lang) => lang != 'Other').map((language) {
              return Chip(
                label: Text(language),
                labelStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 14.sp),
                deleteIcon: Icon(Icons.cancel, size: 18.w, color: AppColors.buttonColor),
                onDeleted: () => controller.removeLanguage(language),
                backgroundColor: AppColors.white,
                side: BorderSide.none,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildInterestMenu(MoreAboutYouController controller) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children:
          controller.interestOptions.map((interest) {
            final isSelected = controller.selectedInterests.contains(interest);
            return ChoiceChip(
              label: Text(interest),
              labelStyle: TextStyles.medium.copyWith(color: isSelected ? AppColors.white : AppColors.primary, fontSize: 14.sp),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
              selectedColor: AppColors.buttonColor,
              backgroundColor: AppColors.white,
              checkmarkColor: isSelected ? AppColors.white : AppColors.primary,
              side: BorderSide(color: AppColors.primary),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  controller.addInterest(interest);
                } else {
                  controller.removeInterest(interest);
                }
              },
            );
          }).toList(),
    );
  }

  /// Save Details
  Future<void> saveDetails(MoreAboutYouController controller) async {
    final profileWatch = ref.read(profileOnboardingController);
    print('controller.selectedSmoking?.toLowerCase() ${controller.selectedFitnessLevel?.toLowerCase()}');

    double height = double.tryParse('${((controller.selectedHeightFeet * 12) + controller.selectedHeightInches) * 2.54}') ?? 0;

    if (controller.formKey.currentState?.validate() ?? false) {
      Map<String, dynamic> map = {
        'bio': controller.bioController.text,
        'interests': controller.selectedInterests,
        'languages': controller.selectedLanguages,
        'height': height.toInt(),
        // 'height': "${controller.selectedHeightFeet}'${controller.selectedHeightInches}\"",
        'dietary_preference': strToEnum(controller.selectedDiet?.toLowerCase() ?? ''),
        'smoking_habit': strToEnum(controller.selectedSmoking?.toLowerCase() ?? ''),
        'drinking_habit': strToEnum(controller.selectedDrinking?.toLowerCase() ?? ''),
        'religion': controller.selectedReligion?.toLowerCase() ?? '',
        'caste': controller.selectedCaste,
        'fitness_level': strToEnum(controller.selectedFitnessLevel?.toLowerCase() ?? ''),
        'education': strToEnum(controller.selectedEducation?.toLowerCase() ?? ''),
        'occupation': strToEnum(controller.selectedOccupation?.toLowerCase() ?? ''),
        'occupation_details': controller.companyController.text,
        'annual_income': controller.selectedIncomeRange,
      };

      await profileWatch.updateProfile(context, map);

      Future.delayed(Duration(milliseconds: 100), () {
        if (profileWatch.updateProfileState.success != null) {
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.partnerPreference());
        }
      });
    }
  }
}

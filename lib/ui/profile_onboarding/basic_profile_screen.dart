import 'package:dateme/framework/controller/profile_onboarding/basic_profile_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/extension/string_extension.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_dropdown.dart';
import 'package:dateme/ui/utils/widgets/common_form_field.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

class BasicProfileScreen extends ConsumerStatefulWidget {
  const BasicProfileScreen({super.key});

  @override
  ConsumerState<BasicProfileScreen> createState() => _BasicProfileScreenState();
}

class _BasicProfileScreenState extends ConsumerState<BasicProfileScreen> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final controller = ref.watch(basicProfileController);
      controller.disposeController(isNotify: true);
      controller.getUserProfile();
    });
  }

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(basicProfileController);
    return commonBackgroundDecoration(
      context,

      child: SafeArea(
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          appBar: CommonAppBar(title: 'Basic Profile', isShowBack: false, isCenterTitle: true),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Form(
                      key: controller.formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildNameField(controller),
                          _buildDateOfBirth(controller),
                          _buildMaritalStatus(controller),
                          _buildIntentStatus(controller),
                          _buildGenderSelection(controller).paddingOnly(bottom: 100.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: _buildSaveButton(controller),
        ),
      ),
    );
  }

  Widget _buildNameField(BasicProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldsHeading('Full Name'),
        CommonInputFormField(
          textEditingController: controller.nameController,
          hintText: 'Enter your name',
          borderColor: AppColors.transparent,
          validator: controller.validateName,
          backgroundColor: AppColors.grey2B2B2B.withAlpha(20),
          readOnly: true,
          textInputType: TextInputType.text,
          textInputAction: TextInputAction.next,
        ),
      ],
    );
  }

  Widget _buildMaritalStatus(BasicProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldsHeading('Marital Status'),
        CommonDropdown(
          value: controller.selectedMaritalStatus,
          hint: 'Select your marital status',
          items: ['Single', 'Divorced', 'Widowed', 'Separated'],
          onChanged: (value) => controller.updateMaritalStatus(value!),
          validator: (value) => controller.validateMaritalStatus(),
        ),
      ],
    );
  }

  Widget _buildIntentStatus(BasicProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldsHeading('What you looking for'),
        CommonDropdown(
          value: controller.selectedIntention,
          hint: 'Select your Intention',
          items: ['Marriage', 'Serious relationship', 'Casual Dating', 'Friendship'],
          onChanged: (value) => controller.updateIntentionStatus(value!),
          validator: (value) => controller.validateIntentionStatus(),
        ),
      ],
    );
  }

  Widget _buildSaveButton(BasicProfileController controller) {
    final profileWatch = ref.watch(profileOnboardingController);
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.transparent,
        boxShadow: [
          BoxShadow(color: AppColors.primary.withAlpha(26), blurRadius: 10, offset: const Offset(0, -2)),
        ],
      ),
      child: CommonButton(
        onTap: () async {
          await controller.saveProfile(ref, context);
        },
        showLoader:
            controller.isLoading ||
            profileWatch.updateProfileState.isLoading ||
            profileWatch.uploadProfileMediaState.isLoading,
        buttonText: 'Save Profile',
      ),
    );
  }

  Widget _buildDateOfBirth(BasicProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldsHeading('Date of Birth'),
        GestureDetector(
          onTap: () async {
            // final date = await showDatePicker(
            //   context: context,
            //   initialDate: DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
            //   firstDate: DateTime(1900),
            //   lastDate: DateTime.now(),
            // );
            // if (date != null) {
            //   controller.updateDateOfBirth(date);
            // }
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
            decoration: BoxDecoration(
              color: AppColors.grey2B2B2B.withAlpha(20),
              borderRadius: BorderRadius.circular(100.r),
              border: Border.all(
                color: controller.dobError != null ? AppColors.redF94008 : AppColors.transparent,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonText(
                  title:
                      controller.selectedDate != null
                          ? DateFormat('MMM dd, yyyy').format(controller.selectedDate!)
                          : 'Select your date of birth',
                  textStyle: TextStyles.regular.copyWith(
                    color: controller.selectedDate != null ? AppColors.black : AppColors.black.withAlpha(120),
                    fontWeight: controller.selectedDate != null ? FontWeight.bold : FontWeight.w400,
                  ),
                ),
                Icon(Icons.calendar_today, size: 20.w, color: AppColors.black),
              ],
            ),
          ),
        ),
        if (controller.dobError != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: CommonText(
              title: controller.dobError!,
              textStyle: TextStyle(color: AppColors.redF94008, fontSize: 12.sp),
            ),
          ),
      ],
    );
  }

  Widget _buildGenderSelection(BasicProfileController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldsHeading('Gender'),

        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            color: AppColors.buttonColor, // Use a dark color for contrast
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: AppColors.white, size: 16.sp).paddingOnly(right: 4.w),
              CommonText(
                title:
                    controller.selectedGender != ''
                        ? controller.selectedGender.capitalizeFirstLetterOfSentence
                        : '',
                textStyle: TextStyles.bold.copyWith(color: AppColors.white),
              ),
            ],
          ),
        ),
        if (controller.genderError != null)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: CommonText(
              title: controller.genderError!,
              textStyle: TextStyle(color: AppColors.redF94008, fontSize: 12.sp),
            ),
          ),
      ],
    );
  }

  /// Fields Heading
  Widget fieldsHeading(String value) {
    return CommonText(
      title: value,
      textStyle: TextStyles.semiBold.copyWith(fontSize: 18.sp, color: AppColors.black),
    ).paddingOnly(top: 20.h, bottom: 8.h);
  }

  Widget fieldsSubHeading(String value) {
    return CommonText(
      title: value,
      maxLines: 10,
      textStyle: TextStyles.semiBold.copyWith(fontSize: 15.sp, color: AppColors.black.withAlpha(150)),
    ).paddingOnly(bottom: 8.h);
  }
}

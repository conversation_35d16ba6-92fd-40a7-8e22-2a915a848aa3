import 'package:dateme/framework/controller/profile_onboarding/policy_confirmation_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/theme/text_style.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PolicyConfirmationScreen extends ConsumerStatefulWidget {
  final String cmsUrl;
  final String? appBarTitle;

  const PolicyConfirmationScreen({super.key, required this.cmsUrl, this.appBarTitle});

  @override
  ConsumerState<PolicyConfirmationScreen> createState() => _PolicyConfirmationScreenState();
}

class _PolicyConfirmationScreenState extends ConsumerState<PolicyConfirmationScreen> {
  bool _hasAcceptedPolicy = false;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final policyWatch = ref.read(policyController);
      policyWatch.disposeController(isNotify: true);
      policyWatch.initializeUrl(widget.cmsUrl);
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final policyWatch = ref.watch(policyController);
    final profileWatch = ref.watch(profileOnboardingController);
    return commonBackgroundDecoration(
      context,
      child: SafeArea(
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (val, pop) async {
            try {
              if (await policyWatch.webViewController?.canGoBack() ?? false) {
                await policyWatch.webViewController?.goBack();
              } else {
                ref.read(navigationStackController).pop();
              }
            } catch (e) {
              showLog('Error $e');
            }
          },
          child: Scaffold(
            appBar: CommonAppBar(
              title: widget.appBarTitle ?? '',
              onBackPressed: () async {
                if (await policyWatch.webViewController?.canGoBack() ?? false) {
                  await policyWatch.webViewController?.goBack();
                } else {
                  ref.read(navigationStackController).pop();
                }
              },
              isShowBack: true,
            ),
            backgroundColor: AppColors.white,
            body: _bodyWidget(),
            bottomNavigationBar: _buildConsentBar(),
          ),
        ),
      ),
    );
  }

  Widget _buildConsentBar() {
    final profileWatch = ref.read(profileOnboardingController);
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(13), blurRadius: 10, offset: const Offset(0, -4)),
        ],
      ),
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Transform.scale(
                scale: 1.2,
                child: Checkbox(
                  value: _hasAcceptedPolicy,
                  onChanged: (value) {
                    setState(() {
                      _hasAcceptedPolicy = value ?? false;
                    });
                  },
                  checkColor: AppColors.white,
                  activeColor: AppColors.buttonColor,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.r)),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  'I have read and agree to the Terms & Conditions and Privacy Policy',
                  style: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.black, height: 1.5),
                ),
              ),
            ],
          ),
          SizedBox(height: 24.h),
          CommonButton(
            buttonText: 'Continue',
            onTap:
                _hasAcceptedPolicy
                    ? () async {
                      Map<String, dynamic> map = {'privacy_policy_accepted': true};

                      await profileWatch.updateProfile(context, map);
                      await Future.delayed(Duration(milliseconds: 100), () {
                        if (profileWatch.updateProfileState.success != null) {
                          ref
                              .read(navigationStackController)
                              .pushAndRemoveAll(const NavigationStackItem.completeProfile());
                        }
                      });
                    }
                    : null,
            height: 56.h,
            showLoader: profileWatch.updateProfileState.isLoading,
            backgroundColor: _hasAcceptedPolicy ? AppColors.buttonColor : AppColors.grey9F9F9F.withAlpha(128),
          ),
        ],
      ),
    );
  }

  Widget _bodyWidget() {
    final policyWatch = ref.watch(policyController);
    return (policyWatch.isLoading)
        ? Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LinearProgressIndicator(
              color: AppColors.primary,
              value: policyWatch.loadingProgress.toDouble() / 100,
              backgroundColor: AppColors.buttonColor.withAlpha(26),
              minHeight: 2.h,
            ),
            Expanded(child: DialogProgressBar(isLoading: policyWatch.isLoading)),
          ],
        )
        : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (policyWatch.webViewController != null)
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.black.withAlpha(13),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  margin: EdgeInsets.all(16.w),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12.r),
                    child: WebViewWidget(controller: policyWatch.webViewController!),
                  ),
                ),
              ),
          ],
        );
  }
}

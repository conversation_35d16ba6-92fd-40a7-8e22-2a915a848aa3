import 'package:dateme/framework/controller/profile_onboarding/partner_preference_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PartnerPreferenceScreen extends ConsumerStatefulWidget {
  const PartnerPreferenceScreen({super.key});

  @override
  ConsumerState<PartnerPreferenceScreen> createState() => _PartnerPreferenceScreenState();
}

class _PartnerPreferenceScreenState extends ConsumerState<PartnerPreferenceScreen> {
  final TextEditingController otherLifestyleController = TextEditingController();

  @override
  void dispose() {
    otherLifestyleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(partnerPreferenceController);

    return commonBackgroundDecoration(
      context,
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          body: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader('Partner Preferences').paddingSymmetric(horizontal: 22.w),
                _buildSubHeader("Tell Us What You're Looking For").paddingSymmetric(horizontal: 22.w),
                SizedBox(height: 20.h),

                // Age Range Section
                _buildSectionCard(
                  title: 'Preferred Age Range',
                  icon: Icons.cake_outlined,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildAgeBox('Min Age', controller.minAge.toInt()),
                          Icon(Icons.arrow_forward, color: AppColors.buttonColor.withAlpha(128)),
                          _buildAgeBox('Max Age', controller.maxAge.toInt()),
                        ],
                      ),
                      SizedBox(height: 20.h),
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          thumbColor: AppColors.buttonColor,
                          overlayColor: AppColors.buttonColor.withAlpha(51),
                          activeTrackColor: AppColors.buttonColor,
                          inactiveTrackColor: AppColors.grey9F9F9F.withAlpha(77),
                          valueIndicatorColor: AppColors.buttonColor,
                          trackHeight: 4.h,
                          thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8.r),
                          overlayShape: RoundSliderOverlayShape(overlayRadius: 16.r),
                        ),
                        child: RangeSlider(
                          values: RangeValues(controller.minAge, controller.maxAge),
                          min: 15,
                          max: 70,
                          divisions: 82,
                          labels: RangeLabels(
                            controller.minAge.toStringAsFixed(0),
                            controller.maxAge.toStringAsFixed(0),
                          ),
                          onChanged: (values) {
                            controller.updateAgeRange(values.start, values.end);
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20.h),

                // Location Radius Section
                _buildSectionCard(
                  title: 'Location Radius',
                  icon: Icons.location_on_outlined,
                  child: Column(
                    children: [
                      _buildRadiusIndicator(controller.radius.toInt()),
                      SizedBox(height: 20.h),
                      SliderTheme(
                        data: SliderTheme.of(context).copyWith(
                          thumbColor: AppColors.buttonColor,
                          activeTrackColor: AppColors.buttonColor,
                          inactiveTrackColor: AppColors.grey9F9F9F.withAlpha(77),
                          trackHeight: 4.h,
                          thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8.r),
                          overlayShape: RoundSliderOverlayShape(overlayRadius: 16.r),
                        ),
                        child: Slider(
                          value: controller.radius,
                          min: 1,
                          max: 100,
                          divisions: 99,
                          onChanged: (value) {
                            controller.updateRadius(value);
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // Lifestyle Section
                _buildSectionCard(
                  title: 'Lifestyle Choices',
                  icon: Icons.style_outlined,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        spacing: 12.w,
                        runSpacing: 12.h,
                        children:
                            controller.lifestyleChoices.map((choice) {
                              final isSelected = controller.selectedLifestyleChoices.contains(choice);
                              return AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                child: FilterChip(
                                  label: Text(choice),
                                  selected: isSelected,
                                  showCheckmark: false,
                                  labelStyle: TextStyles.medium.copyWith(
                                    color: isSelected ? AppColors.white : AppColors.black,
                                    fontSize: 14.sp,
                                  ),
                                  backgroundColor: AppColors.greyD9D9D9.withAlpha(51),
                                  selectedColor: AppColors.buttonColor,
                                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20.r),
                                    side: BorderSide(
                                      color:
                                          isSelected
                                              ? AppColors.buttonColor
                                              : AppColors.grey9F9F9F.withAlpha(77),
                                    ),
                                  ),
                                  onSelected: (isSelected) {
                                    controller.toggleLifestyleChoice(choice);
                                  },
                                ),
                              );
                            }).toList(),
                      ),
                      // if (controller.selectedLifestyleChoices.contains('Other')) ...[
                      //   SizedBox(height: 16.h),
                      //   CommonInputFormField(
                      //     borderColor: AppColors.buttonColor,
                      //     hintText: 'Specify other lifestyle choice',
                      //     textEditingController: otherLifestyleController,
                      //     onChanged: (val) {
                      //       controller.addOtherLifestyleChoice(val);
                      //     },
                      //     // onChanged: ,
                      //     validator: (v) => null,
                      //     contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                      //   ),
                      // ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: CommonButton(
            onTap: () async => await controller.saveProfile(ref, context),
            buttonText: 'Save Preferences',
            showLoader: controller.isLoading,
            height: 56.h,
          ).paddingOnly(left: 20.w, right: 20.w, bottom: 20.h),
        ),
      ),
    );
  }

  Widget _buildAgeBox(String label, int value) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: AppColors.buttonColor.withAlpha(26),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.buttonColor.withAlpha(51)),
      ),
      child: Column(
        children: [
          Text(label, style: TextStyles.regular.copyWith(color: AppColors.grey9F9F9F, fontSize: 12.sp)),
          SizedBox(height: 4.h),
          Text(
            value.toString(),
            style: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 18.sp),
          ),
        ],
      ),
    );
  }

  Widget _buildRadiusIndicator(int radius) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: AppColors.buttonColor.withAlpha(26),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.location_on, color: AppColors.buttonColor, size: 24.w),
          SizedBox(width: 12.w),
          Text('$radius KM', style: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 24.sp)),
        ],
      ),
    );
  }

  Widget _buildSectionCard({required String title, required IconData icon, required Widget child}) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.r), color: AppColors.white),
      child: Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CommonText(
                    title: title,
                    textStyle: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 18.sp),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              child,
            ],
          ),
        ),
      ),
    ).paddingSymmetric(horizontal: 20.w);
  }

  Widget _buildHeader(String title) {
    return CommonText(
      title: title,
      maxLines: 1,
      textStyle: TextStyles.semiBold.copyWith(fontSize: 30.sp, color: AppColors.black),
    );
  }

  Widget _buildSubHeader(String title) {
    return CommonText(
      title: title,
      maxLines: 2,
      textAlign: TextAlign.start,
      textStyle: TextStyles.medium.copyWith(fontSize: 20.sp, color: AppColors.black),
    );
  }
}

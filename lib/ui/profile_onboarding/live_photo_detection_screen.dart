import 'dart:io';

import 'package:camera/camera.dart';
import 'package:dateme/framework/controller/profile_onboarding/live_photo_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

class SimplifiedSelfieScreen extends ConsumerStatefulWidget {
  const SimplifiedSelfieScreen({super.key});

  @override
  ConsumerState<SimplifiedSelfieScreen> createState() => _SimplifiedSelfieScreenState();
}

class _SimplifiedSelfieScreenState extends ConsumerState<SimplifiedSelfieScreen>
    with SingleTickerProviderStateMixin {
  // Camera
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  // Animation and UI State
  late AnimationController _pulseController;
  bool _isCameraReady = false;
  String? _capturedImagePath;
  bool _isDisposed = false; // Add disposal flag

  // Overlay dimensions
  final double _overlayWidth = 300;
  final double _overlayHeight = 400;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _pulseController = AnimationController(duration: const Duration(seconds: 2), vsync: this)
      ..repeat(reverse: true);
  }

  Future<void> _initializeCamera() async {
    if (_isDisposed) return; // Check if disposed before initializing

    try {
      _cameras = await availableCameras();
      if (_cameras != null && _cameras!.isNotEmpty && !_isDisposed) {
        // Use front camera for selfies
        final frontCamera = _cameras!.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.front,
          orElse: () => _cameras!.first,
        );

        _cameraController = CameraController(
          frontCamera,
          ResolutionPreset.high,
          enableAudio: false,
          imageFormatGroup: Platform.isAndroid ? ImageFormatGroup.yuv420 : ImageFormatGroup.bgra8888,
        );

        await _cameraController!.initialize();

        if (mounted && !_isDisposed) {
          setState(() {
            _isCameraReady = true;
          });
        }
      }
    } catch (e) {
      print('Camera initialization error: $e');
      if (mounted && !_isDisposed) {
        setState(() {
          _isCameraReady = false;
        });
      }
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null ||
        !_cameraController!.value.isInitialized ||
        _isDisposed) return;

    try {
      final XFile photo = await _cameraController!.takePicture();

      // Enhance image quality
      final String enhancedPath = await _enhanceImageQuality(photo.path);

      if (mounted && !_isDisposed) {
        setState(() {
          _capturedImagePath = enhancedPath;
        });
      }
    } catch (e) {
      print('Error capturing photo: $e');
    }
  }

  Future<String> _enhanceImageQuality(String originalPath) async {
    try {
      final File originalFile = File(originalPath);
      final Uint8List imageBytes = await originalFile.readAsBytes();

      // Decode image
      img.Image? image = img.decodeImage(imageBytes);
      if (image == null) return originalPath;

      // Apply image enhancements
      // image = img.adjustColor(image, brightness: 1.02, contrast: 1.05, saturation: 1.02);

      // Apply subtle sharpening instead of blur for better quality
      // image = img.convolution(image, filter: [0, -1, 0, -1, 5, -1, 0, -1, 0]);

      // Save enhanced image
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String enhancedPath = '${appDir.path}/enhanced_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final File enhancedFile = File(enhancedPath);
      await enhancedFile.writeAsBytes(img.encodeJpg(image, quality: 92));

      return enhancedPath;
    } catch (e) {
      print('Image enhancement error: $e');
      return originalPath;
    }
  }

  void _retakePhoto() {
    if (_isDisposed) return;
    setState(() {
      _capturedImagePath = null;
    });
  }

  Future<void> _confirmPhoto() async {
    if (_capturedImagePath == null || _isDisposed) return;

    try {
      final profileWatch = ref.read(profileOnboardingController);

      await profileWatch.uploadProfileMedia(context, [_capturedImagePath ?? ''], ref);

      if (!_isDisposed && mounted) {
        if (profileWatch.uploadProfileMediaState.success?.uploaded?.isNotEmpty ?? false) {
          final controller = ref.read(livePhotoControllerProvider);
          controller.updatePhotoCaptureStatus(false);
          controller.updatePhotoPath('');
          await profileWatch.getUserProfile(ref);

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Selfie uploaded successfully!'), backgroundColor: Colors.green),
          );

          controller.getPhotosStatus();
          controller.profilePhoto();
          ref.read(navigationStackController).pop();
        }
      }
    } catch (e) {
      print('Error confirming photo: $e');
      if (mounted && !_isDisposed) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error uploading photo'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  void dispose() {
    _isDisposed = true; // Set disposal flag first

    // Dispose camera controller with proper error handling
    _cameraController?.dispose().catchError((error) {
      print('Error disposing camera controller: $error');
    });
    _cameraController = null;

    // Dispose animation controller
    _pulseController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isDisposed) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(child: CircularProgressIndicator(color: Colors.white)),
      );
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
          child: _capturedImagePath != null ? _buildPhotoPreview() : _buildCameraView()
      ),
    );
  }

  Widget _buildCameraView() {
    if (!_isCameraReady || _cameraController == null || _isDisposed) {
      return const Center(child: CircularProgressIndicator(color: Colors.white));
    }

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              // Camera Preview with mirroring for front camera
              if (_cameraController != null && _cameraController!.value.isInitialized)
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()..rotateY(3.14159), // Mirror front camera
                  child: CameraPreview(_cameraController!),
                ),

              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Header Instructions
                  _buildHeaderInstructions(),
                  // Overlay
                  _buildOverlay(),
                ],
              ),
            ],
          ),
        ),

        // Capture Controls
        Container(
          height: 120,
          child: _buildCaptureControls(),
        ),
      ],
    );
  }

  Widget _buildOverlay() {
    return Center(
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Container(
            height: _overlayHeight,
            width: _overlayWidth,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white.withAlpha(((0.5 + (_pulseController.value * 0.5) * 255).round())),
                width: 3,
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Stack(
              children: [
                // Corner brackets
                ...List.generate(4, (index) {
                  final isLeft = index < 2;
                  final isTop = index % 2 == 0;
                  return _buildCornerBracket(isLeft, isTop, Colors.white);
                }),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCornerBracket(bool isLeft, bool isTop, Color color) {
    return Positioned(
      left: isLeft ? -3 : null,
      right: !isLeft ? -3 : null,
      top: isTop ? -3 : null,
      bottom: !isTop ? -3 : null,
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: Colors.transparent,
          border: Border(
            left: BorderSide(color: isLeft ? color : Colors.transparent, width: 3),
            top: BorderSide(color: isTop ? color : Colors.transparent, width: 3),
            right: BorderSide(color: !isLeft ? color : Colors.transparent, width: 3),
            bottom: BorderSide(color: !isTop ? color : Colors.transparent, width: 3),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderInstructions() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      color: Colors.black.withAlpha(179),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Position yourself in the frame and tap to capture',
              style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w600),
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () {
              if (!_isDisposed) {
                Navigator.of(context).pop();
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCaptureControls() {
    return Center(
      child: GestureDetector(
        onTap: _isDisposed ? null : _capturePhoto,
        child: Container(
          height: 80,
          width: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 4),
          ),
          child: Container(
            margin: const EdgeInsets.all(4),
            decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoPreview() {
    if (_isDisposed || _capturedImagePath == null) {
      return const Center(child: CircularProgressIndicator(color: Colors.white));
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        // Mirror the captured image to match the preview
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.identity()..rotateY(3.14159),
          child: Image.file(File(_capturedImagePath!), fit: BoxFit.cover),
        ),
        Positioned(
          bottom: 50,
          left: 0,
          right: 0,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                  icon: Icons.close,
                  onTap: _isDisposed ? () {} : _retakePhoto,
                  color: Colors.red
              ),
              _buildActionButton(
                  icon: Icons.check,
                  onTap: _isDisposed ? () {} : _confirmPhoto,
                  color: Colors.green
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({required IconData icon, required VoidCallback onTap, required Color color}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 60,
        width: 60,
        decoration: BoxDecoration(shape: BoxShape.circle, color: color.withAlpha(204)),
        child: Icon(icon, color: Colors.white, size: 30),
      ),
    );
  }
}
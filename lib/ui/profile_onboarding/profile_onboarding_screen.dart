import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:dateme/framework/controller/profile_onboarding/identity_verification/mobile_verification_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/ui/routing/navigation_stack_item.dart';
import 'package:dateme/ui/routing/stack.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/text_style.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProfileOnboardingScreen extends ConsumerStatefulWidget {
  const ProfileOnboardingScreen({super.key});

  @override
  ConsumerState<ProfileOnboardingScreen> createState() => _ProfileOnboardingScreenState();
}

class _ProfileOnboardingScreenState extends ConsumerState<ProfileOnboardingScreen> {
  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      final profileOnboardingWatch = ref.watch(profileOnboardingController);
      profileOnboardingWatch.disposeController(isNotify: true);
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: commonBackgroundDecoration(context, child: SafeArea(child: _buildContent())),
    );
  }

  Widget _buildContent() {
    final mobileVerificationWatch = ref.watch(mobileVerificationController);
    return SingleChildScrollView(
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // SizedBox(height: 60.h),
            // _buildHeaderTexts(),
            // SizedBox(height: 40.h),
            // _buildAnimatedDescription(),
            // const Spacer(),
            // _buildGetStartedButton(),


            SizedBox(height: 30.h),
            CommonButton(onTap: () {}, buttonText: 'Adhaar Verification'),
            SizedBox(height: 30.h),
            CommonButton(onTap: () {}, buttonText: 'Selfie Verification'),
            SizedBox(height: 30.h),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderTexts() {
    return Column(
      children: [
        DefaultTextStyle(
          style: TextStyles.bold.copyWith(fontSize: 30.sp, color: AppColors.white),
          child: AnimatedTextKit(
            animatedTexts: [
              WavyAnimatedText(
                LocaleKeys.keyWelcomeToDateMe,
                speed: const Duration(milliseconds: 300),
              ),
            ],
            isRepeatingAnimation: false,
          ),
        ),
        SizedBox(height: 10.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
          decoration: BoxDecoration(
            color: AppColors.white.withAlpha(230), // 0.9 * 255 ≈ 230
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withAlpha(26), // 0.1 * 255 ≈ 26
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: CommonText(
            title: LocaleKeys.keyFindYourPerfectMatch,
            textStyle: TextStyles.medium.copyWith(fontSize: 20.sp, color: AppColors.primary),
          ),
        ),
      ],
    );
  }

  Widget _buildAnimatedDescription() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: AppColors.white.withAlpha(230), // 0.9 * 255 ≈ 230
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withAlpha(26), // 0.1 * 255 ≈ 26
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: AnimatedTextKit(
        animatedTexts: [
          TypewriterAnimatedText(
            LocaleKeys.keyWhereLoveStoriesBegin,
            textStyle: TextStyles.semiBold.copyWith(fontSize: 24.sp, color: AppColors.clrBlue),
            speed: const Duration(milliseconds: 100),
            cursor: '❤️',
          ),
        ],
        repeatForever: true,
        displayFullTextOnTap: true,
      ),
    );
  }

  Widget _buildGetStartedButton() {
    return CommonButton(
      height: 60.h,
      onTap: () {
        ref.read(navigationStackController).push(NavigationStackItem.locationPermission());
      },
      buttonText: LocaleKeys.keyLetsGetStarted,
    );
  }
}

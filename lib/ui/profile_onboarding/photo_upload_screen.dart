import 'package:dateme/framework/controller/profile_onboarding/photo_upload_controller.dart';
import 'package:dateme/framework/controller/profile_onboarding/profile_onboarding_controller.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PhotoUploadScreen extends ConsumerStatefulWidget {
  const PhotoUploadScreen({super.key});

  @override
  ConsumerState<PhotoUploadScreen> createState() => _PhotoUploadScreenState();
}

class _PhotoUploadScreenState extends ConsumerState<PhotoUploadScreen> {
  /// For Bottom Sheet
  bool _isFirstTime = true;
  int currentIndex = 0;
  PageController pageController = PageController(initialPage: 0);

  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final photoUploadWatch = ref.watch(photoUploadController);
      photoUploadWatch.disposeController(isNotify: true);
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    final photoUploadWatch = ref.watch(photoUploadController);

    return commonBackgroundDecoration(
      context,

      child: SafeArea(
        child: Scaffold(
          backgroundColor: AppColors.transparent,
          body: SafeArea(
            child: Column(
              children: [
                Expanded(child: SingleChildScrollView(padding: EdgeInsets.symmetric(horizontal: 20.w), child: Form(key: photoUploadWatch.formKey, child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [_buildPhotoUpload(photoUploadWatch).paddingOnly(bottom: 100.h)])))),
              ],
            ),
          ),
          bottomNavigationBar: _buildSaveButton(photoUploadWatch),
        ),
      ),
    );
  }

  Widget _buildPhotoUpload(PhotoUploadController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            fieldsHeading('Profile Photos'),
            InkWell(
              onTap: () {
                _showPhotoUploadBottomSheet(context);
              },
              child: Icon(Icons.info_outline, color: AppColors.primary),
            ),
          ],
        ),
        fieldsSubHeading('Upload 3 or more photos Your first photo will be your profile photo the rest will be shown as your gallery posts.'),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 3, crossAxisSpacing: 8.w, mainAxisSpacing: 8.h),
          itemCount: maxPickImageCount,
          itemBuilder: (context, index) {
            if (controller.selectedImages.isNotEmpty) {
              return index < controller.selectedImages.length ? _buildPhotoItem(controller, index) : _buildAddPhotoButton(controller);
            }
            return _buildAddPhotoButton(controller);
          },
        ),
        if (controller.imageError != null) Padding(padding: EdgeInsets.only(top: 4.h), child: CommonText(title: controller.imageError!, textStyle: TextStyle(color: AppColors.redF94008, fontSize: 12.sp))),
      ],
    );
  }

  void _showPhotoUploadBottomSheet(BuildContext context) {
    final photoUploadWatch = ref.watch(photoUploadController);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      builder: (context) {
        return SafeArea(
          child: StatefulBuilder(
            builder: (context, setState) {
              return SizedBox(
                height: MediaQuery.of(context).size.height * 0.5,
                child: Column(
                  children: [
                    const SizedBox(height: 8),
                    Container(height: 4, width: 40, decoration: BoxDecoration(color: AppColors.greyBCBCBC, borderRadius: BorderRadius.circular(2))),
                    const SizedBox(height: 16),
                    CommonText(title: 'First impressions matter!', textStyle: TextStyles.bold.copyWith(fontSize: 24.sp, color: AppColors.black)),
                    const SizedBox(height: 16),
                    Expanded(
                      child: Column(
                        children: [
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.3,
                            child: PageView(
                              controller: pageController,
                              onPageChanged: (value) {
                                setState(() {
                                  currentIndex = value; // Update the current index
                                });
                              },
                              children: [_buildPhotoUploadSlide('Avoid heavy filters or excessive makeup'), _buildPhotoUploadSlide('Use front-facing, well-lit shots'), _buildPhotoUploadSlide('We love your friends, but this one’s just about you')],
                            ),
                          ),
                          Row(mainAxisAlignment: MainAxisAlignment.center, children: List.generate(3, (index) => Container(margin: const EdgeInsets.symmetric(horizontal: 4), height: 8, width: 8, decoration: BoxDecoration(color: index == currentIndex ? AppColors.black : AppColors.greyBCBCBC, shape: BoxShape.circle)))),
                          SizedBox(height: 20.h),
                          CommonButton(
                            height: 55.h,
                            backgroundColor: AppColors.buttonColor,
                            buttonTextColor: AppColors.white,
                            buttonText: currentIndex < 2 ? 'Next' : 'Continue',
                            onTap: () {
                              if (currentIndex < 2) {
                                pageController.animateToPage(currentIndex + 1, duration: const Duration(milliseconds: 300), curve: Curves.ease);
                              } else {
                                Navigator.pop(context);
                                if (photoUploadWatch.selectedImages.length < maxPickImageCount) {
                                  photoUploadWatch.addImages();
                                } else {
                                  photoUploadWatch.imageError = 'You can only add up to 6 photos';
                                  photoUploadWatch.updateWidget();
                                }
                              }
                            },
                          ).paddingSymmetric(horizontal: 20.w),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildPhotoUploadSlide(String text) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(mainAxisAlignment: MainAxisAlignment.center, children: [Container(height: 130.h, width: 130.h, decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(12.r))), SizedBox(width: 16.w), Container(height: 130.h, width: 130.h, decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(12.r)))]),
        const SizedBox(height: 16),
        CommonText(title: text, textAlign: TextAlign.center, maxLines: 10, textStyle: TextStyles.semiBold.copyWith(fontSize: 22.sp, color: AppColors.black)),
        const SizedBox(height: 16),
      ],
    ).paddingSymmetric(horizontal: 30.w);
  }

  Widget _buildAddPhotoButton(PhotoUploadController photoUploadWatch) {
    return InkWell(
      onTap: () {
        if (_isFirstTime) {
          _showPhotoUploadBottomSheet(context);
          _isFirstTime = false;
        } else {
          if (photoUploadWatch.selectedImages.length < maxPickImageCount) {
            photoUploadWatch.addImages();
          } else {
            photoUploadWatch.imageError = 'You can only add up to 6 photos';
            photoUploadWatch.updateWidget();
          }
        }
      },
      child: Container(decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(8), border: Border.all(color: AppColors.primary.withAlpha(77))), child: Icon(Icons.add_photo_alternate_outlined, color: AppColors.primary, size: 30.w)),
    );
  }

  Widget _buildPhotoItem(PhotoUploadController photoUploadWatch, int index) {
    return Stack(
      children: [
        ClipRRect(borderRadius: BorderRadius.circular(8), child: Image.file(photoUploadWatch.selectedImages[index], fit: BoxFit.cover, width: double.infinity, height: double.infinity)),
        Positioned(top: 4, right: 4, child: InkWell(onTap: () => photoUploadWatch.removeImage(index), child: Container(padding: EdgeInsets.all(4.w), decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle, boxShadow: [BoxShadow(color: Colors.black.withAlpha(77), blurRadius: 4)]), child: Icon(Icons.close, size: 16.w, color: AppColors.redF94008)))),
      ],
    );
  }

  Widget _buildSaveButton(PhotoUploadController photoUploadWatch) {
    final profileWatch = ref.watch(profileOnboardingController);
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(color: AppColors.transparent, boxShadow: [BoxShadow(color: AppColors.primary.withAlpha(26), blurRadius: 10, offset: const Offset(0, -2))]),
      child: CommonButton(
        onTap: () async {
          await photoUploadWatch.saveProfile(ref, context);
        },
        showLoader: photoUploadWatch.isLoading || profileWatch.updateProfileState.isLoading || profileWatch.uploadProfileMediaState.isLoading,
        buttonText: 'Save Profile',
      ),
    );
  }

  /// Fields Heading
  Widget fieldsHeading(String value) {
    return CommonText(title: value, textStyle: TextStyles.semiBold.copyWith(fontSize: 18.sp, color: AppColors.black)).paddingOnly(top: 20.h, bottom: 8.h);
  }

  Widget fieldsSubHeading(String value) {
    return CommonText(title: value, maxLines: 10, textStyle: TextStyles.semiBold.copyWith(fontSize: 15.sp, color: AppColors.black.withAlpha(150))).paddingOnly(bottom: 8.h);
  }
}

import 'package:dateme/framework/controller/other_profile/others_profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class OthersProfileScreen extends ConsumerStatefulWidget {
  const OthersProfileScreen({super.key, required this.userId});

  final String userId;

  @override
  ConsumerState<OthersProfileScreen> createState() => _OthersProfileScreenState();
}

class _OthersProfileScreenState extends ConsumerState<OthersProfileScreen> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final othersProfileWatch = ref.watch(othersProfileController);
      othersProfileWatch.disposeController(isNotify: true);
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return Scaffold(body: _bodyWidget());
  }

  ///Body Widget
  Widget _bodyWidget() {
    return Container();
  }
}

import 'package:dateme/framework/controller/connection/connection_controller.dart';
import 'package:dateme/framework/repository/connection/model/incoming_request_response_model.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/analytics/analytics-events.constant.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_app_bar.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_image.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:dateme/ui/utils/widgets/dialog_progressbar.dart';
import 'package:dateme/ui/utils/widgets/empty_state_widget.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ConnectionScreenMobile extends ConsumerStatefulWidget {
  const ConnectionScreenMobile({super.key});

  @override
  ConsumerState<ConnectionScreenMobile> createState() => _ConnectionScreenMobileState();
}

class _ConnectionScreenMobileState extends ConsumerState<ConnectionScreenMobile>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final connectionWatch = ref.read(connectionController);
      connectionWatch.disposeController(isNotify: true);

      /// Track Connections event
      AnalyticsEvents.connectionScreen.track();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return commonBackgroundDecoration(
      context,
      child: Scaffold(
        appBar: CommonAppBar(
          title: 'Connections',
          isCenterTitle: true,
          isShowBack: false,
          bottom: _buildTabBar(),
        ),
        backgroundColor: AppColors.transparent,
        body: _buildBody(),
      ),
    );
  }

  PreferredSize _buildTabBar() {
    return PreferredSize(
      preferredSize: Size.fromHeight(56.h),
      child: Container(
        color: AppColors.white,
        child: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          labelStyle: TextStyles.semiBold.copyWith(fontSize: 12.sp),
          unselectedLabelStyle: TextStyles.medium.copyWith(fontSize: 14.sp),
          onTap: (val) async {
            final connectionWatch = ref.read(connectionController);
            showLog('Val $val');
            if (val == 0) {
              /// Track Connections event
              AnalyticsEvents.incomingConnection.track();
              await connectionWatch.incomingConnectionRequestAPI();
            } else if (val == 1) {
              AnalyticsEvents.outgoingConnection.track();
              await connectionWatch.outgoingConnectionRequestsAPI();
            } else if (val == 2) {
              AnalyticsEvents.myConnection.track();
              await connectionWatch.myConnectionRequestsAPI();
            } else {
              AnalyticsEvents.myLikedConnections.track();
            }
          },
          tabs: [
            _buildTab(Icons.arrow_downward_rounded, 'Incoming'),
            _buildTab(Icons.arrow_upward_rounded, 'Outgoing'),
            _buildTab(Icons.people_alt_rounded, 'Connected'),
            _buildTab(Icons.favorite_rounded, 'Liked'),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(IconData icon, String text) {
    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [Icon(icon, size: 20.sp), SizedBox(width: 4.w) /*, CommonText(title: text)*/],
      ),
    );
  }

  Widget _buildBody() {
    return TabBarView(
      physics: NeverScrollableScrollPhysics(),
      controller: _tabController,
      children: [
        _IncomingConnectionsTab().paddingOnly(bottom: 100.h),
        _OutgoingConnectionsTab().paddingOnly(bottom: 100.h),
        _MyConnectionsTab().paddingOnly(bottom: 100.h),
        _LikedConnectionsTab().paddingOnly(bottom: 100.h),
      ],
    );
  }
}

class _ConnectionCard extends StatelessWidget {
  final String imageUrl;
  final String name;
  final String age;
  final List<Widget> actions;

  const _ConnectionCard({
    required this.imageUrl,
    required this.name,
    required this.age,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(color: AppColors.black.withAlpha(20), blurRadius: 15, offset: const Offset(0, 4)),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(12.sp),
        child: Row(
          children: [
            _buildProfileImage(),
            SizedBox(width: 16.w),
            Expanded(child: _buildUserInfo()),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileImage() {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: AppColors.grey8A8A8A, width: 2),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(1000.r),
            child: CommonImage(strIcon: imageUrl, height: 70.h, width: 70.h, boxFit: BoxFit.cover),
          ),
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            padding: EdgeInsets.all(4.sp),
            decoration: BoxDecoration(
              color: AppColors.secondary,
              shape: BoxShape.circle,
              border: Border.all(color: AppColors.white, width: 2),
            ),
            child: Icon(Icons.check, color: AppColors.white, size: 12.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CommonText(title: name, textStyle: TextStyles.bold.copyWith(fontSize: 18.sp)),
        SizedBox(height: 4.h),
        Row(
          children: [
            Icon(Icons.cake_outlined, size: 16.sp, color: AppColors.textSecondary),
            SizedBox(width: 4.w),
            CommonText(
              title: '$age years',
              textStyle: TextStyles.medium.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children:
          actions.map((action) {
            return Container(margin: EdgeInsets.only(left: 8.w), child: action);
          }).toList(),
    );
  }
}

class _IncomingConnectionsTab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionWatch = ref.watch(connectionController);
    return connectionWatch.incomingRequestState.isLoading || connectionWatch.respondApiState.isLoading
        ? DialogProgressBar(isLoading: true)
        : (!connectionWatch.incomingRequestState.isLoading && connectionWatch.incomingRequest.isEmpty)
        ? EmptyStateWidget(
          title: 'No Incoming Requests Found',
          description: 'Update to your profile to get more visibility ',
        )
        : ListView.builder(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          itemBuilder: (context, index) {
            final item = connectionWatch.incomingRequest[index];
            return _ConnectionCard(
              imageUrl: item.receiver?.profilePicture ?? commonDummyProfileImage,
              name: item.sender?.fullName ?? '',
              age: item.sender?.age.toString() ?? '',
              actions: [
                _buildIconButtonWithBorder(
                  icon: Icons.check,
                  color: AppColors.green,
                  onTap: () => _acceptConnection(ref, item.connectionHashid ?? ''),
                ),
                _buildIconButtonWithBorder(
                  icon: Icons.close_rounded,
                  color: AppColors.red,
                  onTap: () => _rejectConnection(ref, item.connectionHashid ?? ''),
                ),
              ],
            );
          },
          itemCount: connectionWatch.incomingRequest.length,
        );
  }
}

class _OutgoingConnectionsTab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionWatch = ref.watch(connectionController);
    return connectionWatch.outgoingRequestState.isLoading || connectionWatch.respondApiState.isLoading
        ? DialogProgressBar(isLoading: true)
        : (!connectionWatch.outgoingRequestState.isLoading && connectionWatch.outgoingRequest.isEmpty)
        ? EmptyStateWidget(
          title: 'No Outgoing Requests Found',
          description: 'Try to send connection request to profile you like',
        )
        : ListView.builder(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          itemBuilder: (context, index) {
            final item = connectionWatch.outgoingRequest[index];
            return _ConnectionCard(
              imageUrl: item.receiver?.profilePicture ?? commonDummyProfileImage,
              name: item.receiver?.fullName ?? '',
              age: item.receiver?.age.toString() ?? '',
              actions: [
                _buildIconButtonWithBorder(
                  icon: Icons.close_rounded,
                  color: AppColors.red,
                  onTap: () => _cancelRequest(ref, item.receiver?.userHashid ?? ''),
                ),
              ],
            );
          },
          itemCount: connectionWatch.outgoingRequest.length,
        );
  }
}

class _MyConnectionsTab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionWatch = ref.watch(connectionController);
    return connectionWatch.myConnectionState.isLoading
        ? DialogProgressBar(isLoading: true)
        : (!connectionWatch.myConnectionState.isLoading && connectionWatch.myConnectionsList.isEmpty)
        ? EmptyStateWidget(
          title: 'No Connections  Found',
          description: 'Try to send connection request to profile you like and get matched.',
        )
        : ListView.builder(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          itemBuilder: (context, index) {
            Connection connection = connectionWatch.myConnectionsList[index];
            Receiver? receiver = connection.receiver;
            Sender? sender = connection.sender;
            return receiver != null
                ? _ConnectionCard(
                  imageUrl: receiver.profilePicture ?? commonDummyProfileImage,
                  name: receiver.fullName ?? 'Emma Wilson',
                  age: receiver.age.toString(),
                  actions: [
                    _buildIconButtonWithBorder(
                      icon: Icons.chat_bubble_outline_rounded,
                      color: AppColors.buttonColorDark,
                      onTap: () => _startChat(ref, 'userId'),
                    ),
                    _buildIconButtonWithBorder(
                      icon: Icons.block,
                      color: AppColors.red,
                      onTap: () => _showBlockDialog(context, ref, 'userId'),
                    ),
                  ],
                )
                : _ConnectionCard(
                  imageUrl: sender?.profilePicture ?? commonDummyProfileImage,
                  name: sender?.fullName ?? 'Emma Wilson',
                  age: sender?.age.toString() ?? '27',
                  actions: [
                    _buildIconButtonWithBorder(
                      icon: Icons.chat_bubble_outline_rounded,
                      color: AppColors.buttonColorDark,
                      onTap: () => _startChat(ref, 'userId'),
                    ),
                    _buildIconButtonWithBorder(
                      icon: Icons.block,
                      color: AppColors.red,
                      onTap: () => _showBlockDialog(context, ref, 'userId'),
                    ),
                  ],
                );
          },
          itemCount: connectionWatch.myConnectionsList.length,
        );
  }
}

class _LikedConnectionsTab extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      itemBuilder: (context, index) {
        return _ConnectionCard(
          imageUrl: commonDummyProfileImage,
          name: 'Michael Brown',
          age: '26',
          actions: [
            _buildGradientButton(
              icon: Icons.favorite_border_rounded,
              text: 'Connect',
              onTap: () => _sendConnection(context, ref, 'userId'),
            ),
          ],
        );
      },
      itemCount: 10,
    );
  }
}

Widget _buildIconButtonWithBorder({
  required IconData icon,
  required Color color,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(100.r),
      child: Container(
        decoration: BoxDecoration(shape: BoxShape.circle),
        child: Icon(icon, color: color, size: 24.sp),
      ),
    ),
  );
}

Widget _buildGradientButton({required IconData icon, required String text, required VoidCallback onTap}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(100.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(100.r)),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: AppColors.white, size: 20.sp),
            SizedBox(width: 8.w),
            CommonText(
              title: text,
              textStyle: TextStyles.semiBold.copyWith(color: AppColors.white, fontSize: 14.sp),
            ),
          ],
        ),
      ),
    ),
  );
}

Future<void> _showBlockDialog(BuildContext context, WidgetRef ref, String userId) async {
  return showDialog(
    context: context,
    builder:
        (context) => Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
          child: Container(
            padding: EdgeInsets.all(24.sp),
            decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(20.r)),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(16.sp),
                  decoration: BoxDecoration(color: AppColors.red.withAlpha(25), shape: BoxShape.circle),
                  child: Icon(Icons.block_outlined, color: AppColors.red, size: 32.sp),
                ),
                SizedBox(height: 16.h),
                CommonText(
                  title: 'Block User',
                  textStyle: TextStyles.bold.copyWith(fontSize: 20.sp, color: AppColors.textPrimary),
                ),
                SizedBox(height: 8.h),
                CommonText(
                  title:
                      'Are you sure you want to block this user? You won\'t see their profile or receive messages from them.',
                  textStyle: TextStyles.regular.copyWith(fontSize: 14.sp, color: AppColors.textSecondary),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24.h),
                Row(
                  children: [
                    Expanded(
                      child: CommonButton(
                        buttonText: 'Cancel',
                        onTap: () => Navigator.pop(context),
                        backgroundColor: AppColors.red,
                        buttonTextColor: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: CommonButton(
                        buttonText: 'Block',
                        onTap: () {
                          _blockUser(ref, userId);
                          Navigator.pop(context);
                        },
                        backgroundColor: AppColors.red,
                        buttonTextColor: AppColors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
  );
}

Future<void> _acceptConnection(WidgetRef ref, String connectionId) async {
  AnalyticsEvents.connectionAccept.track(parameters: {'connection_id': connectionId, 'type': 'accepted'});
  await ref.read(connectionController).respondConnectionApi(connectionId, 'accepted');
  await ref.read(connectionController).incomingConnectionRequestAPI();
}

Future<void> _rejectConnection(WidgetRef ref, String connectionId) async {
  AnalyticsEvents.connectionReject.track(parameters: {'connection_id': connectionId, 'type': 'rejected'});
  ref.read(connectionController).respondConnectionApi(connectionId, 'rejected');
  await ref.read(connectionController).incomingConnectionRequestAPI();
}

void _cancelRequest(WidgetRef ref, String connectionId) {
  AnalyticsEvents.connectionCancel.track(parameters: {'connection_id': connectionId, 'type': 'canceled'});
  ref.read(connectionController).respondConnectionApi(connectionId, 'destroyed');
}

void _startChat(WidgetRef ref, String userId) {
  // ref.read(connectionController).startChat(userId);
}

void _blockUser(WidgetRef ref, String userId) {
  // ref.read(connectionController).blockUser(userId);
}

Future<void> _sendConnection(BuildContext context, WidgetRef ref, String userId) async {
  // onSendConnection(context: context, ref: ref, userHashid: userId);
  // await ref.read(connectionController).myConnectionRequestsAPI();
}

///Body Widget
// Widget _bodyWidget() {
//   final connectionScreenWatch = ref.read(connectionController);
//   final walletScreenWatch = ref.watch(walletController);
//   return Column(
//     children: [
//       SizedBox(height: 100.h),
//       // CommonButton(
//       //   buttonText: 'Send Connection',
//       //   onTap: () async {
//       //     await connectionScreenWatch.sendConnectionAPI('vBQWO7');
//       //
//       //     await Future.delayed(Duration(milliseconds: 100), () async {
//       //       if (mounted && connectionScreenWatch.sendConnectionState.success != null) {
//       //         RazorPayPaymentSuccessModel? paymentSuccessResponse = await RazorpayManager.instance.createPayment(
//       //           context: context,
//       //           amount: connectionScreenWatch.sendConnectionState.success?.amount ?? 0,
//       //           orderId: connectionScreenWatch.sendConnectionState.success?.orderId ?? '',
//       //         );
//       //
//       //         await Future.delayed(Duration(milliseconds: 100), () async {
//       //           if (paymentSuccessResponse != null) {
//       //             await connectionScreenWatch.verifyPaymentAPI(ref, paymentSuccessResponse);
//       //
//       //             await Future.delayed(Duration(milliseconds: 100), () async {
//       //               if (connectionScreenWatch.verifyPaymentState.success?.success ?? false) {
//       //                 showMessageDialog(context, 'Payment Successful', () {});
//       //               }
//       //             });
//       //           }
//       //         });
//       //       }
//       //     });
//       //   },
//       // ),
//
//       SizedBox(height: 10),
//     ],
//   ).paddingOnly(left: 20.w, right: 20.w);
// }

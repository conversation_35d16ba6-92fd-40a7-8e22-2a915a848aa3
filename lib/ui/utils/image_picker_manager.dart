// import 'dart:io';
// import 'dart:typed_data';
//
// import 'package:Bonno/framework/utils/extension/extension.dart';
// import 'package:Bonno/framework/utils/extension/string_extension.dart';
// import 'package:Bonno/ui/utils/const/app_constants.dart';
// import 'package:Bonno/ui/utils/theme/app_strings.g.dart';
// import 'package:Bonno/ui/utils/theme/assets.gen.dart';
// import 'package:Bonno/ui/utils/theme/theme.dart';
// import 'package:Bonno/ui/utils/widgets/common_image.dart';
// import 'package:Bonno/ui/utils/widgets/common_text.dart';
// import 'package:crop_your_image/crop_your_image.dart';
// import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:image_cropper/image_cropper.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:path_provider/path_provider.dart';
//
// /*
// Required permissions for iOS
// NSCameraUsageDescription :- ${PRODUCT_NAME} is require camera permission to choose user profile photo.
// NSPhotoLibraryUsageDescription :- ${PRODUCT_NAME} is require photos permission to choose user profile photo.
//
// Required permissions for Android
// <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
// <uses-permission android:name="android.permission.CAMERA"/>
//
// <!--Image Cropper-->
//        <activity
//            android:name="com.yalantis.ucrop.UCropActivity"
//            android:exported="true"
//            android:screenOrientation="portrait"
//            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
// * */
//
// class ImagePickerManager {
//   ImagePickerManager._privateConstructor();
//
//   static final ImagePickerManager instance = ImagePickerManager._privateConstructor();
//
//   final ImagePicker picker = ImagePicker();
//
//   var imgSelectOption = {
//     'Camera',
//     'Gallery',
//   };
//
//   /*
//   Open Picker
//   Usage:- File? file = await ImagePickerManager.instance.openPicker(context);
//   * */
//
//   Future<FileResult?> openPicker(
//     BuildContext context, {
//     String? title,
//     double? ratioX,
//     double? ratioY,
//     CropStyle? cropStyle,
//     Function? onRemoveCallBack,
//   }) async {
//     String type = '';
//     String subType = '';
//     if (context.mounted) {
//       await showModalBottomSheet(
//         context: context,
//         backgroundColor: Colors.transparent,
//         barrierColor: AppColors.black.withAlpha(77),
//         builder: (BuildContext context) {
//           return StatefulBuilder(
//             builder: (context, state) {
//               return Container(
//                 margin: EdgeInsets.only(bottom: 20.h, left: 20.w, right: 20.w),
//                 decoration: BoxDecoration(
//                   color: AppColors.white,
//                   borderRadius: BorderRadius.circular(16.r),
//                   boxShadow: [
//                     BoxShadow(
//                       offset: const Offset(0, -4),
//                       blurRadius: 12.r,
//                       spreadRadius: 0,
//                       color: AppColors.black.withAlpha(31),
//                     ),
//                     BoxShadow(
//                       offset: const Offset(0, 4),
//                       blurRadius: 12.r,
//                       spreadRadius: 0,
//                       color: AppColors.black.withAlpha(26),
//                     ),
//                   ],
//                 ),
//                 padding: EdgeInsets.only(left: 29.w, right: 29.w),
//                 child: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     SizedBox(height: 20.h),
//                     Row(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       mainAxisAlignment: MainAxisAlignment.spaceAround,
//                       children: [
//                         /// Camera
//                         InkWell(
//                           onTap: () async {
//                             subType = LocaleKeys.keyImage.localized;
//                             type = LocaleKeys.keyCamera.localized;
//                             Navigator.pop(context);
//                           },
//                           child: commonImageTitleWidget(Assets.svgs.svgPickFromCamera.path, LocaleKeys.keyCamera.localized, null),
//                         ),
//
//                         /// Gallery
//
//                         InkWell(
//                           onTap: () {
//                             subType = LocaleKeys.keyImage.localized;
//                             type = LocaleKeys.keyGallery.localized;
//                             Navigator.pop(context);
//                           },
//                           child: commonImageTitleWidget(Assets.svgs.svgPickFromGallery.path, LocaleKeys.keyGallery.localized, null),
//                         ),
//
//                         /// Document
//                         // if (onRemoveCallBack == null)
//                         //   commonImageTitleWidget(Assets.svgs.svgPickDocument.path, LocaleKeys.keyDocument.localized, () {
//                         //     type = LocaleKeys.keyDocument.localized;
//                         //     Navigator.pop(context);
//                         //   }),
//                       ],
//                     ),
//                     SizedBox(height: 20.h),
//                     Row(
//                       children: [
//                         Expanded(
//                           child: InkWell(
//                             onTap: () {
//                               Navigator.pop(context);
//                             },
//                             child: Container(
//                               width: double.infinity,
//                               height: 50.h,
//                               decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(10.r)),
//                               alignment: Alignment.center,
//                               child: Text(
//                                 LocaleKeys.keyCancel.localized,
//                                 style: TextStyles.semiBold.copyWith(color: AppColors.redLight),
//                               ),
//                             ),
//                           ),
//                         ),
//                         if (onRemoveCallBack != null) SizedBox(width: 10.w),
//                         Visibility(
//                           visible: onRemoveCallBack != null,
//                           child: Expanded(
//                             child: InkWell(
//                               onTap: () {
//                                 Navigator.pop(context);
//                                 onRemoveCallBack?.call();
//                               },
//                               child: Container(
//                                 width: double.infinity,
//                                 height: 50.h,
//                                 decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(10.r)),
//                                 alignment: Alignment.center,
//                                 child: Text(
//                                   LocaleKeys.keyRemove.localized,
//                                   style: TextStyles.semiBold.copyWith(color: AppColors.secondaryPrimary),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               );
//             },
//           );
//         },
//       );
//     }
//
//     File? croppedFile;
//     if (type.isNotEmpty) {
//       /// Document
//       if (type == LocaleKeys.keyDocument.localized) {
//         FilePickerResult? result = await FilePicker.platform.pickFiles();
//
//         if (result != null) {
//           List<File> files = result.paths.map((path) => File(path!)).toList();
//           if (files.isNotEmpty) {
//             croppedFile = files.first;
//           }
//         } else {
//           commonToaster('Canceled by used');
//         }
//       }
//
//       /// Camera
//       else if (type == LocaleKeys.keyCamera.localized) {
//         XFile? pickedFile = (subType == LocaleKeys.keyImage.localized)
//             ? await picker.pickImage(
//                 source: ImageSource.camera,
//               )
//             : await picker.pickVideo(
//                 source: ImageSource.camera,
//               );
//
//         showLog('pickedFile At Camera: $pickedFile');
//
//         if (pickedFile != null && pickedFile.path != '' && subType == LocaleKeys.keyImage.localized) {
//           // CroppedFile? cropImage = (await cropper.cropImage(sourcePath: pickedFile.path));
//           //
//           // if (cropImage != null && cropImage.path != '') {
//           //   croppedFile = File(cropImage.path);
//           // }
//
//           File? cropImage;
//           final route = MaterialPageRoute(
//             builder: (context) => ImageCropperScreen(imagePath: pickedFile.path),
//           );
//           await Navigator.push(context, route).then((v) {
//             cropImage = v;
//             if (cropImage != null && cropImage?.path != '') {
//               croppedFile = File(cropImage!.path);
//             }
//           });
//         } else if (pickedFile != null && pickedFile.path != '' && subType == LocaleKeys.keyVideo.localized) {
//           croppedFile = File(pickedFile.path);
//         }
//       } else if (type == LocaleKeys.keyGallery.localized) {
//         XFile? pickedFile = (subType == LocaleKeys.keyImage.localized)
//             ? await picker.pickImage(
//                 source: ImageSource.gallery,
//               )
//             : await picker.pickVideo(
//                 source: ImageSource.gallery,
//               );
//
//         showLog('pickedFile At Gallery: $pickedFile');
//
//         if (pickedFile != null && pickedFile.path != '' && subType == LocaleKeys.keyImage.localized) {
//           // CroppedFile? cropImage = (await cropper.cropImage(
//           //   sourcePath: pickedFile.path,
//           // ));
//           //
//           // if (cropImage != null && cropImage.path != '') {
//           //   croppedFile = File(cropImage.path);
//           // }
//
//           File? cropImage;
//           final route = MaterialPageRoute(
//             builder: (context) => ImageCropperScreen(imagePath: pickedFile.path),
//           );
//           await Navigator.push(context, route).then((v) {
//             cropImage = v;
//             if (cropImage != null && cropImage?.path != '') {
//               croppedFile = File(cropImage!.path);
//             }
//           });
//         } else if (pickedFile != null && pickedFile.path != '' && subType == LocaleKeys.keyVideo.localized) {
//           croppedFile = File(pickedFile.path);
//         }
//       }
//       // showLog('croppedFile $croppedFile');
//       // showLog('type $type');
//       // showLog('type $subType');
//       if (croppedFile != null) {
//         return FileResult(file: croppedFile!, fileType: getFileType(type, subType));
//       } else {
//         return null;
//       }
//     } else {
//       return null;
//     }
//   }
//
//   getFileType(String type, String subtype) {
//     if (type == LocaleKeys.keyCamera.localized) {
//       return subtype;
//     } else if (type == LocaleKeys.keyGallery.localized) {
//       return subtype;
//     } else {
//       return type;
//     }
//   }
//
//   /*
//   Open Multi Picker
//   Usage:- Future<List<File>?> files = ImagePickerManager.instance.openMultiPicker(context);
//   * */
//   // Future<List<File>?> openMultiPicker(BuildContext context, RequestType type, {int maxAssets = 1}) async {
//   //   final List<AssetEntity>? result = await AssetPicker.pickAssets(
//   //     context,
//   //     pickerConfig: AssetPickerConfig(
//   //       maxAssets: maxAssets,
//   //       themeColor: AppColors.primary,
//   //       requestType: type,
//   //     ),
//   //   );
//   //
//   //   List<File> files = [];
//   //   if ((result ?? []).isNotEmpty) {
//   //     for (final AssetEntity entity in result!) {
//   //       final File? file = await entity.file;
//   //       files.add(file!);
//   //     }
//   //   }
//   //
//   //   return files;
//   // }
//
//   Future<int> openMultipleImagePicker(BuildContext context, {String? title}) async {
//     String str = '';
//
//     await showModalBottomSheet(
//         context: context,
//         backgroundColor: Colors.transparent,
//         barrierColor: AppColors.black.withAlpha(77),
//         builder: (BuildContext context) {
//           return StatefulBuilder(builder: (context, state) {
//             return Container(
//               color: AppColors.transparent,
//               padding: EdgeInsets.only(left: 29.w, right: 29.w),
//               height: 225.h,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Container(
//                     width: double.infinity,
//                     decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(10.r)),
//                     child: Column(
//                       children: [
//                         Visibility(
//                           visible: title != null && title.isNotEmpty,
//                           child: Padding(
//                             padding: EdgeInsets.only(top: 20.h, bottom: 5.h),
//                             child: Text(
//                               title ?? ''.localized,
//                               maxLines: 2,
//                               style: TextStyles.medium.copyWith(fontSize: 18.sp),
//                             ),
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () {
//                             Navigator.pop(context);
//                             str = imgSelectOption.elementAt(0);
//                           },
//                           child: Container(
//                             padding: EdgeInsets.only(top: 30.h, bottom: 15.h),
//                             alignment: Alignment.center,
//                             child: Text(
//                               LocaleKeys.keyCamera.localized,
//                               style: TextStyles.medium.copyWith(color: AppColors.primary),
//                             ),
//                           ),
//                         ),
//                         Padding(
//                           padding: EdgeInsets.only(left: 20.w, right: 20.w),
//                           child: const Divider(
//                             height: 1,
//                             color: AppColors.greyLight,
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () {
//                             Navigator.pop(context);
//                             str = imgSelectOption.elementAt(1);
//                           },
//                           child: Container(
//                             padding: EdgeInsets.only(top: 15.h, bottom: 30.h),
//                             alignment: Alignment.center,
//                             child: Text(
//                               LocaleKeys.keyGallery.localized,
//                               style: TextStyles.medium.copyWith(color: AppColors.primary),
//                             ),
//                           ),
//                         )
//                       ],
//                     ),
//                   ),
//                   SizedBox(height: 10.h),
//                   InkWell(
//                     onTap: () {
//                       Navigator.pop(context);
//                     },
//                     child: Container(
//                       width: double.infinity,
//                       height: 50.h,
//                       decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(10.r)),
//                       alignment: Alignment.center,
//                       child: Text(
//                         LocaleKeys.keyCancel.localized,
//                         style: TextStyles.semiBold.copyWith(color: AppColors.redLight),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             );
//           });
//         });
//     return str == 'camera' ? 0 : (str == 'gallery' ? 1 : -1);
//   }
//
//   /// Common Image Title widget
//   commonImageTitleWidget(String image, String title, Function? onTap) {
//     return IgnorePointer(
//       ignoring: onTap == null,
//       child: InkWell(
//         onTap: () {
//           if (onTap != null) {
//             onTap.call();
//           }
//         },
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             CommonImage(
//               strIcon: image,
//               height: 40.h,
//               width: 40.h,
//               boxFit: BoxFit.scaleDown,
//               // imgColor: AppColors.primary,
//             ),
//             CommonText(
//               title: title,
//               textStyle: TextStyles.regular.copyWith(
//                 color: AppColors.primary,
//                 fontSize: 13.sp,
//               ),
//             ).paddingOnly(top: 10.h),
//           ],
//         ),
//       ),
//     );
//   }
//
//   ///Handle Document After Picker
// // handleDocumentAfterPicker(BuildContext context, Function(List<File>) resultBlock) async {
// //   List<File> files = [];
// //   FilePickerResult? result = await FilePicker.platform.pickFiles(allowMultiple: true,type: FileType.custom, allowedExtensions: ['pdf', 'doc', 'docx'],);
// //
// //   if(result != null) {
// //     // files = result.paths.map((path) => PickedFile(path ?? "")).toList();
// //     files = result.paths.map((path) => File(path ?? "")).toList();
// //   }
// //   resultBlock(files);
// // }
// }
//
// class FileResult {
//   final File file;
//   final String fileType;
//
//   FileResult({required this.file, required this.fileType});
// }
//
// // cropper_screen.dart
// class ImageCropperScreen extends StatefulWidget {
//   final String imagePath;
//
//   const ImageCropperScreen({super.key, required this.imagePath});
//
//   @override
//   State<ImageCropperScreen> createState() => _ImageCropperScreenState();
// }
//
// class _ImageCropperScreenState extends State<ImageCropperScreen> {
//   final _controller = CropController();
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppColors.primary,
//       appBar: AppBar(title: const Text('Crop Image')),
//       body: Center(
//         child: Container(
//           padding: EdgeInsets.all(2.sp),
//           width: MediaQuery.of(context).size.width * 0.9,
//           height: MediaQuery.of(context).size.width * 0.9,
//           color: AppColors.secondaryPrimary,
//           child: Crop(
//             controller: _controller,
//             image: File(widget.imagePath).readAsBytesSync(),
//             onCropped: (result) async {
//               if (result is CropSuccess) {
//                 final Uint8List croppedBytes = result.croppedImage;
//                 final tempDir = await getTemporaryDirectory();
//                 final filePath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}.png';
//
//                 final croppedFile = File(filePath);
//                 await croppedFile.writeAsBytes(croppedBytes);
//                 Navigator.pop(context, croppedFile);
//               } else if (result is CropFailure) {
//                 /// Handle crop failure (optional)
//                 showLog('Crop failed: ${result.cause}');
//               }
//             },
//             initialRectBuilder: InitialRectBuilder.withBuilder((viewportRect, imageRect) {
//               final double left = viewportRect.left + (viewportRect.width * 0.1);
//               final double top = viewportRect.top + (viewportRect.height * 0.1);
//               final double width = viewportRect.width * 0.8;
//               final double height = viewportRect.height * 0.8;
//
//               return Rect.fromLTWH(left, top, width, height);
//             }),
//             interactive: true,
//             baseColor: Colors.black,
//             maskColor: Colors.black.withAlpha(100),
//             cornerDotBuilder: (size, edgeAlignment) => const DotControl(),
//           ),
//         ),
//       ),
//       floatingActionButton: FloatingActionButton(
//         onPressed: () => _controller.crop(),
//         child: const Icon(Icons.check),
//       ),
//     );
//   }
// }

import 'package:dateme/framework/utils/session.dart';
import 'package:flutter/material.dart';

class AppColors {
  AppColors._();

  // Color(0xFF5B2C87), // Deep Purple
  //     Color(0xFF7E3AA5), // Royal Purple
  //     Color(0xFF9C5CC6), // Violet
  //     Color(0xFFC06BD4), // Magenta

  static bool isDarkMode = (Session.getIsAppThemeDark() ?? false);

  // static const Color primary = Color(0xff7E3AA5);
  // static const Color secondary = Color(0xFFC06BD4);
  // static const Color primary = Color(0xffE6DAF0);
  // static const Color secondary = Color(0xFFC8E6C9);
  static const Color primary = Color(0xffBD152A);
  static const Color primaryLight = Color(0xfffcf8f8);
  static const Color secondary = Color(0xffBD152A);
  static const Color buttonColor = Color(0xffBD152A);
  static const Color buttonColorDark = Color(0xffBD152A);
  static const Color topHeadingColor = Color(0xff1DAD58);
  static const Color subHeadingColor = Color(0xffF2F7B6);
  static const Color clrCCCCCC = Color(0xff666666);
  static const Color cardBackground = Color(0xFFF8F9FA);
  static const Color textPrimary = Color(0xFF1A1818);
  static const Color textSecondary = Color(0xFF1B1E1E);
  static const Color dividerColor = Color(0xFFECF0F1);

  // static const Color violet = Color(0xff9C5CC6);
  // static const Color deepPurple = Color(0xff5B2C87);
  static const Color link = Color(0xff1b76d2);
  static const Color white = Color(0xffffffff);
  static const Color golden = Color(0xFFFFD700); // Pure gold
  static const Color darkGolden = Color(0xFFDAA520); // Goldenrod
  static const Color lightGolden = Color(0xFFFFF8DC); // Cornsilk
  static const Color metalGolden = Color(0xFFB8860B); // DarkGoldenrod

  static const Color black = Color(0xff000000);
  static const Color black0E0E0E = Color(0xff0E0E0E);
  static const Color activeColor = Color(0xfff3b25f);
  static const Color redF94008 = Color(0xffF94008);
  static const Color darkRed = Color(0xFFD9251C);

  // static const Color orangeEF5C24 = Color(0xffEF5C24);
  static const Color black010101 = Color(0xff252020);
  static const Color green = Color(0xff02D169);
  static const Color greenEAFD86 = Color(0xffEAFD86);
  static const Color greyBCBCBC = Color(0xffBCBCBC);
  static const Color black292929 = Color(0xff292929);
  static const Color transparent = Color(0x00000000);
  static const Color greyF7F8F8 = Color(0xffF7F8F8);
  static const Color black070707 = Color(0xff070707);
  static const Color black1C1C1C = Color(0xff1C1C1C);
  static const Color black162A33 = Color(0xff162A33);
  static const Color black282D33 = Color(0xff282D33);
  static const Color lightGrey = Color(0xffE8E8EC);
  static const Color darkGreen = Color(0xff5B6F70);
  static const Color bg1A2D3170 = Color(0x1A2D3170);
  static Color shimmerEffectHighlight = const Color(0xFFDFDFDF).withValues(alpha: 0.4);
  static const Color shimmerEffectBaseColor = Color(0xFFDFDFDF);
  static const Color grey5B5B5B = Color(0xff5B5B5B);
  static const Color greyCFCFCF = Color(0xffCFCFCF);
  static const Color grey8A8A8A = Color(0xff8A8A8A);
  static const Color grey6B6B6B = Color(0xff6B6B6B);
  static const Color greyF6F6F6 = Color(0xffF6F6F6);
  static const Color grey2B2B2B = Color(0xff2B2B2B);
  static const Color grey9F9F9F = Color(0xff9F9F9F);
  static const Color greyEEEEEE = Color(0xffEEEEEE);

  static const Color red = Color(0xffE34850);
  static const Color lightRed = Color(0xffFA6063);
  static const Color greyFBFBFB = Color(0xffFBFBFB);
  static const Color greyD9D9D9 = Color(0xffD9D9D9);
  static const Color grey55D9D9D9 = Color(0x55D9D9D9);
  static const Color greyD1D1D1 = Color(0xffD1D1D1);

  static const Color yellowFCF2DE = Color(0xffFCF2DE);
  static const Color blueE5ECF6 = Color(0xffE5ECF6);
  static const Color blueE3F5FF = Color(0xffE3F5FF);
  static const Color blueBFF1EC = Color(0xffBFF1EC);
  static const Color blueCFDAFF = Color(0xffCFDAFF);
  static const Color blueA4BFEB = Color(0xffA4BFEB);
  static const Color greenC7E8A1 = Color(0xffC7E8A1);
  static const Color green60A80D = Color(0xff60A80D);
  static const Color purple = Color(0xffD3BED4);
  static const Color black1C2121 = Color(0xff1C2121);
  static const Color greenECFFD6 = Color(0xffECFFD6);

  static const Color clrBlue = Color(0xff0DA5ED);

  static MaterialColor colorPrimary = MaterialColor(0xffBD152A, colorSwathes);

  static Map<int, Color> colorSwathes = {
    50: const Color.fromRGBO(189, 21, 42, .1),
    100: const Color.fromRGBO(189, 21, 42, .2),
    200: const Color.fromRGBO(189, 21, 42, .3),
    300: const Color.fromRGBO(189, 21, 42, .4),
    400: const Color.fromRGBO(189, 21, 42, .5),
    500: const Color.fromRGBO(189, 21, 42, .6),
    600: const Color.fromRGBO(189, 21, 42, .7),
    700: const Color.fromRGBO(189, 21, 42, .8),
    800: const Color.fromRGBO(189, 21, 42, .9),
    900: const Color.fromRGBO(189, 21, 42, 1),
  };

  static Color textByTheme() => isDarkMode ? white : primary;

  // static Color textByLightPrimary() => isDarkMode ? darkGreen : primary;

  static Color textGreyByTheme() => isDarkMode ? white : white;

  static Color searchFontByTheme() => isDarkMode ? primary : white;

  static Color buttonBGGreyByTheme() => isDarkMode ? primary : white;

  static Color imageColorByTheme() => isDarkMode ? primary : white;

  static Color drawerBgByTheme() => isDarkMode ? black : white;

  static Color textMainFontByTheme() => isDarkMode ? white : black;

  static Color whiteBlackByTheme() => isDarkMode ? white : black;

  static Color textLightGreyByTheme() => isDarkMode ? white : grey5B5B5B;

  static Color darkByScaffoldTheme() => isDarkMode ? black : white;

  static Color scaffoldBGByTheme() => isDarkMode ? black : white;

  static Color greyScaffoldBGByTheme() => isDarkMode ? black : greyFBFBFB;

  static Color textDarkGreyByTheme() => isDarkMode ? grey5B5B5B : primary;

  static Color cardBGByTheme() => isDarkMode ? grey5B5B5B : white;

  static Color buttonFGByTheme(bool isOn) =>
      isOn
          ? white
          : isDarkMode
          ? white
          : primary;

  static Color buttonBGByTheme(bool isOn) =>
      isOn
          ? isDarkMode
              ? primary
              : white
          : isDarkMode
          ? primary
          : grey5B5B5B;

  static Color buttonBorderByTheme() => isDarkMode ? white : primary;

  static Color dividerByTheme() => isDarkMode ? white : grey5B5B5B;

  static Color dialogBGByTheme() => isDarkMode ? white : black;

  static Color textFieldTextByTheme() => isDarkMode ? primary : grey5B5B5B;

  static Color textFieldBorderColorByTheme() => isDarkMode ? primary : grey5B5B5B;

  static Color textFieldDisableBorderColorByTheme() => isDarkMode ? white : transparent;

  static Color suggestionTextByTheme() => isDarkMode ? primary : grey8A8A8A;

  static Color whiteBlackNewByTheme() => isDarkMode ? white : grey8A8A8A;

  static Color blackWhiteByTheme() => isDarkMode ? white : black;

  static Color blackNewLightPurpleByTheme() => isDarkMode ? black : primary;

  static Color darkPurpleWhiteByTheme() => isDarkMode ? white : primary;

  static Color primaryWhiteByTheme() => isDarkMode ? white : primary;

  static Color whiteGreyNewByTheme() => isDarkMode ? white : black;

  static Color greyTransparentByTheme() => isDarkMode ? transparent : grey8A8A8A;

  static Color greyNewWhiteByTheme() => isDarkMode ? white : grey8A8A8A;

  static Color darkGreyByTheme() => isDarkMode ? grey2B2B2B : greyF6F6F6;

  static Color lightGreyByTheme() => isDarkMode ? white : grey2B2B2B;

  static Color textWhiteByNewBlack() => isDarkMode ? white : black070707;

  static Color textWhiteByNewBlack2() => isDarkMode ? white : black162A33;

  static Color cardBGDarkGrey() => isDarkMode ? grey5B5B5B : greyF7F8F8;

  static Color textByLightGreen() => isDarkMode ? white : darkGreen;

  static const List<Color> allColors = [
    Color(0xFFFFFFFF),
    Color(0xFF000000),
    Color(0xFFC85068),
    Color(0xFFF0B030),
    Color(0xFF38A8C0),
    Color(0xFF9878C8),
    Color(0xFFE06050),
    Color(0xFF9898A8),
    Color(0xFFE81000),
    Color(0xFFF08800),
    Color(0xFFF8D000),
    Color(0xFF70C020),
    Color(0xFF3078B8),
    Color(0xFF0000FF),
    Color(0xFFB8C828),
    Color(0xFF3078B8),
    Color(0xFFE0E0B8),
    Color(0xFFF08898),
    Color(0xFFE8E0C0),
    Color(0xFFA8E8B8),
    Color(0xFFB0D8E0),
    Color(0xFFD898E8),
    Color(0xFF9860B8),
    Color(0xFFFE4178),
    Color(0xFF41FEC7),
  ];

  // static Color textByTheme() => isDarkMode ? white : primary;
}

import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:flutter/material.dart';

DatePickerThemeData getDatePickerTheme() {
  return DatePickerThemeData(
    // Background color of the date picker dialog
    backgroundColor: AppColors.isDarkMode ? AppColors.black1C1C1C : AppColors.white,

    // Surface tint color
    surfaceTintColor: AppColors.transparent,

    // Elevation
    elevation: 8,

    // Shadow color
    shadowColor:
        AppColors.isDarkMode ? AppColors.black.withAlpha(128) : AppColors.grey8A8A8A.withAlpha(77),

    // Shape of the dialog
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
      side: BorderSide(
        color: AppColors.isDarkMode ? AppColors.primary.withAlpha(77) : AppColors.dividerColor,
        width: 1,
      ),
    ),

    // Header colors
    headerBackgroundColor: AppColors.white,

    headerForegroundColor: AppColors.primary,

    // Header text style
    headerHeadlineStyle: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: AppColors.isDarkMode ? AppColors.white : AppColors.primary,
    ),

    headerHelpStyle: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColors.isDarkMode ? AppColors.white.withAlpha(204) : AppColors.primary.withAlpha(204),
    ),

    // Week day labels style
    weekdayStyle: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColors.isDarkMode ? AppColors.grey8A8A8A : AppColors.textSecondary,
    ),

    // Day text style
    dayStyle: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppColors.isDarkMode ? AppColors.white : AppColors.textPrimary,
    ),

    // Day foreground color (for normal days)
    dayForegroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.white;
      }
      if (states.contains(WidgetState.disabled)) {
        return AppColors.isDarkMode ? AppColors.grey5B5B5B : AppColors.grey8A8A8A;
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary;
      }
      return AppColors.isDarkMode ? AppColors.white : AppColors.textPrimary;
    }),

    // Day background color
    dayBackgroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary.withAlpha(26);
      }
      return AppColors.transparent;
    }),

    // Day overlay color (for press effects)
    dayOverlayColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.pressed)) {
        return AppColors.primary.withAlpha(51);
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary.withAlpha(26);
      }
      return AppColors.transparent;
    }),

    // Today's date highlight
    todayForegroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.white;
      }
      return AppColors.primary;
    }),

    todayBackgroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      return AppColors.transparent;
    }),

    todayBorder: BorderSide(color: AppColors.primary, width: 2),

    // Year style
    yearStyle: TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: AppColors.isDarkMode ? AppColors.white : AppColors.textPrimary,
    ),

    // Year foreground color
    yearForegroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.white;
      }
      if (states.contains(WidgetState.disabled)) {
        return AppColors.isDarkMode ? AppColors.grey5B5B5B : AppColors.grey8A8A8A;
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary;
      }
      return AppColors.isDarkMode ? AppColors.white : AppColors.textPrimary;
    }),

    // Year background color
    yearBackgroundColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppColors.primary;
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary.withAlpha(26);
      }
      return AppColors.transparent;
    }),

    // Year overlay color
    yearOverlayColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.pressed)) {
        return AppColors.primary.withAlpha(51);
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary.withAlpha(26);
      }
      return AppColors.transparent;
    }),

    // Range selection colors
    rangePickerBackgroundColor: AppColors.isDarkMode ? AppColors.black1C1C1C : AppColors.white,

    rangePickerSurfaceTintColor: AppColors.transparent,

    rangePickerElevation: 8,

    rangePickerShadowColor:
        AppColors.isDarkMode ? AppColors.black.withAlpha(128) : AppColors.grey8A8A8A.withAlpha(77),

    rangePickerShape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(16),
      side: BorderSide(
        color: AppColors.isDarkMode ? AppColors.primary.withAlpha(77) : AppColors.dividerColor,
        width: 1,
      ),
    ),

    rangePickerHeaderBackgroundColor: AppColors.isDarkMode ? AppColors.primary : AppColors.primary,

    rangePickerHeaderForegroundColor: AppColors.isDarkMode ? AppColors.white : AppColors.primary,

    rangePickerHeaderHeadlineStyle: TextStyle(
      fontSize: 24,
      fontWeight: FontWeight.w600,
      color: AppColors.isDarkMode ? AppColors.white : AppColors.primary,
    ),

    rangePickerHeaderHelpStyle: TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w500,
      color: AppColors.isDarkMode ? AppColors.white.withAlpha(204) : AppColors.primary.withAlpha(204),
    ),

    rangeSelectionBackgroundColor: AppColors.primary.withAlpha(51),

    rangeSelectionOverlayColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.pressed)) {
        return AppColors.primary.withAlpha(77);
      }
      if (states.contains(WidgetState.hovered)) {
        return AppColors.primary.withAlpha(26);
      }
      return AppColors.transparent;
    }),

    // Divider color
    dividerColor: AppColors.isDarkMode ? AppColors.grey5B5B5B : AppColors.dividerColor,

    // Input decoration theme for text input mode
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.isDarkMode ? AppColors.grey2B2B2B : AppColors.greyF7F8F8,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.isDarkMode ? AppColors.grey5B5B5B : AppColors.dividerColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.isDarkMode ? AppColors.grey5B5B5B : AppColors.dividerColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.red, width: 1),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.red, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      hintStyle: TextStyle(color: AppColors.isDarkMode ? AppColors.grey8A8A8A : AppColors.grey6B6B6B),
    ),

    // Cancel button style
    cancelButtonStyle: ButtonStyle(
      textStyle: WidgetStateProperty.all(
        TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.isDarkMode ? AppColors.grey8A8A8A : AppColors.textSecondary,
        ),
      ),
      foregroundColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.isDarkMode ? AppColors.grey6B6B6B : AppColors.grey5B5B5B;
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.isDarkMode ? AppColors.white : AppColors.textPrimary;
        }
        return AppColors.isDarkMode ? AppColors.grey8A8A8A : AppColors.textSecondary;
      }),
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.grey8A8A8A.withAlpha(51);
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.grey8A8A8A.withAlpha(26);
        }
        return AppColors.transparent;
      }),
      padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
      shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
    ),

    // Confirm button style (using primary as requested)
    confirmButtonStyle: ButtonStyle(
      textStyle: WidgetStateProperty.all(
        const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white),
      ),
      foregroundColor: WidgetStateProperty.all(AppColors.white),
      backgroundColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.primary.withAlpha(204);
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.primary.withAlpha(230);
        }
        if (states.contains(WidgetState.disabled)) {
          return AppColors.grey8A8A8A;
        }
        return AppColors.primary;
      }),
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return AppColors.white.withAlpha(51);
        }
        if (states.contains(WidgetState.hovered)) {
          return AppColors.white.withAlpha(26);
        }
        return AppColors.transparent;
      }),
      padding: WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 24, vertical: 12)),
      shape: WidgetStateProperty.all(RoundedRectangleBorder(borderRadius: BorderRadius.circular(8))),
      elevation: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.pressed)) {
          return 2;
        }
        if (states.contains(WidgetState.hovered)) {
          return 4;
        }
        return 2;
      }),
    ),
  );
}

// Usage example:
// In your ThemeData, add:
// datePickerTheme: getDatePickerTheme(),

import 'package:dateme/framework/utils/anim/fade_box_transition.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/framework/utils/session.dart';
import 'package:dateme/ui/utils/theme/locale_keys.g.dart';
import 'package:dateme/ui/utils/theme/theme.dart';
import 'package:dateme/ui/utils/widgets/common_button.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Confirmation dialog  message
showConfirmationDialog(
  BuildContext context,
  String title,
  String message,
  String btn1Name,
  String btn2Name,
  Function(bool isPositive) didTakeAction,
) {
  return showDialog(
    barrierDismissible: true,
    context: context,
    barrierColor: AppColors.bg1A2D3170,
    builder:
        (context) => Dialog(
          backgroundColor: AppColors.white,
          insetPadding: EdgeInsets.all(16.sp),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ScreenUtil().setWidth(22.r))),
          child: FadeBoxTransition(
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 22.h, bottom: 15.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      message == '' ? SizedBox(height: 20.h) : const SizedBox(),
                      Text(
                        title,
                        textAlign: TextAlign.center,
                        style: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 20.sp),
                      ),
                      message != '' ? SizedBox(height: 18.h) : const SizedBox(),
                      Text(
                        message,
                        textAlign: TextAlign.center,
                        style: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 20.sp),
                      ),
                      SizedBox(height: 26.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CommonButton(
                            width: 139.w,
                            height: 49.h,
                            buttonText: btn1Name,

                            borderRadius: BorderRadius.circular(30.r),
                            borderWidth: 1.w,
                            onTap: () {
                              Navigator.pop(context);
                              Future.delayed(const Duration(milliseconds: 80), () {
                                didTakeAction(true);
                              });
                            },

                            buttonTextColor: AppColors.white,
                          ),
                          SizedBox(width: 15.w),
                          CommonButton(
                            buttonText: btn2Name,
                            width: 139.w,
                            height: 49.h,
                            borderWidth: 1.w,
                            borderRadius: BorderRadius.circular(30.r),
                            onTap: () {
                              Navigator.pop(context);
                              Future.delayed(const Duration(milliseconds: 80), () {
                                didTakeAction(false);
                              });
                            },
                            buttonTextColor: AppColors.white,
                          ),
                        ],
                      ),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
  );
}

/// Message Dialog
showMessageDialog(BuildContext context, String message, Function()? didDismiss, {String? title}) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    barrierColor: AppColors.grey8A8A8A.withAlpha(128),
    builder:
        (context) => FadeBoxTransition(
          child: Dialog(
            backgroundColor: AppColors.white,
            surfaceTintColor: AppColors.white,
            insetPadding: EdgeInsets.all(16.sp),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                mainAxisSize: MainAxisSize.min,
                children: [
                  title != null
                      ? CommonText(
                        title: title,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        textStyle: TextStyles.bold.copyWith(color: AppColors.black, fontSize: 22.sp),
                      ).paddingOnly(bottom: 10.h)
                      : const Offstage(),
                  CommonText(
                    title: message,
                    textAlign: TextAlign.center,
                    maxLines: 5,
                    textStyle: TextStyles.medium.copyWith(
                      color: AppColors.black.withAlpha(200),
                      fontSize: 14.sp,
                    ),
                  ),
                  SizedBox(height: 20.h),
                  CommonButton(
                    buttonTextColor: AppColors.white,
                    borderColor: AppColors.primary,
                    width: 150.w,
                    buttonText: LocaleKeys.keyOk,
                    onTap: () {
                      Navigator.pop(context);
                      if (didDismiss != null) {
                        Future.delayed(const Duration(milliseconds: 80), () {
                          didDismiss();
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
  );
}

/// Logout Dialog
showLogoutDialog(BuildContext context) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    barrierColor: AppColors.black.withValues(alpha: 0.3),
    builder:
        (context) => Consumer(
          builder: (context, ref, _) {
            return Dialog(
              backgroundColor: AppColors.white,
              surfaceTintColor: AppColors.white,
              insetPadding: EdgeInsets.all(16.sp),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ScreenUtil().setWidth(10.r))),
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  SizedBox(
                    width: double.infinity,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: CommonText(
                              title: LocaleKeys.keySessionExpired,
                              textAlign: TextAlign.center,
                              maxLines: 5,
                              textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 16.sp),
                            ),
                          ),
                          SizedBox(height: 24.h),
                          CommonButton(
                            buttonTextColor: AppColors.white,
                            // backgroundColor: AppColors.primary,
                            borderColor: AppColors.primary,
                            width: 150.w,
                            buttonText: LocaleKeys.keySignIn,
                            onTap: () {
                              Session.sessionLogout(ref);
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
  );
}

/// Widget Dialog
showWidgetDialog(
  BuildContext context,
  Widget? widget,
  Function()? didDismiss, {
  bool isDismissDialog = false,
  int autoDismissTimer = 0,
  GlobalKey? dialogKey,
}) {
  showDialog(
    barrierDismissible: isDismissDialog,
    context: context,
    barrierColor: AppColors.greyBCBCBC.withAlpha(120),
    builder:
        (context) => Dialog(
          key: dialogKey,
          surfaceTintColor: AppColors.white,
          backgroundColor: AppColors.white,
          insetPadding: EdgeInsets.all(20.sp),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ScreenUtil().setWidth(20.r))),
          child: Wrap(children: [const Align(alignment: Alignment.topRight, child: CloseButton()), widget!]),
        ),
  );

  if (autoDismissTimer > 0) {
    Future.delayed(Duration(seconds: autoDismissTimer), () {
      if (didDismiss != null) {
        didDismiss();
      }
    });
  } else {
    if (isDismissDialog) {
      Future.delayed(const Duration(milliseconds: 1000), () {
        didDismiss!();
      });
    }
  }
}

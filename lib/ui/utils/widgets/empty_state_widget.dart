import 'package:dateme/framework/provider/network/network.dart';
import 'package:dateme/framework/utils/extension/context_extension.dart';
import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/theme/app_colors.dart';
import 'package:dateme/ui/utils/theme/text_style.dart';
import 'package:dateme/ui/utils/widgets/common_text.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EmptyStateWidget extends StatelessWidget {
  final String? icon;
  final String? title;
  final String? description;
  final double? height;

  const EmptyStateWidget({super.key, this.icon, this.title, this.description, this.height});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // height: height ?? context.height * 0.7,
      width: context.width,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 10.h),
                CommonText(
                  title: title ?? 'No Data Found',
                  textStyle: TextStyles.semiBold.copyWith(fontSize: 18.sp, color: AppColors.black),
                ),
                SizedBox(height: 10.h),
                CommonText(
                  title: description ?? '',
                  textAlign: TextAlign.center,
                  maxLines: 10,
                  textStyle: TextStyles.medium.copyWith(fontSize: 16.sp, color: AppColors.black),
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ],
        ).paddingOnly(left: 16.w, right: 16.w),
      ),
    );
  }
}

// lib/ui/utils/widgets/common_dropdown.dart

import 'package:dateme/framework/utils/extension/extension.dart';
import 'package:dateme/ui/utils/theme/theme.dart';

class CommonDropdown extends StatelessWidget {
  final String? value;
  final String hint;
  final List<String> items;
  final Function(String?) onChanged;
  final String? Function(String?)? validator;
  final IconData? prefixIcon;
  final bool alignedDropdown;
  final Color? dropdownColor;
  final Color? borderColor;
  final Color? textColor;
  final TextStyle? hintStyle;
  final TextStyle? itemStyle;

  const CommonDropdown({
    super.key,
    this.value,
    required this.hint,
    required this.items,
    required this.onChanged,
    this.validator,
    this.prefixIcon,
    this.alignedDropdown = true,
    this.dropdownColor,
    this.borderColor,
    this.textColor,
    this.hintStyle,
    this.itemStyle,
  });

  @override
  Widget build(BuildContext context) {
    return ButtonTheme(
      alignedDropdown: alignedDropdown,
      child: DropdownButtonFormField<String>(
        value: value,
        borderRadius: BorderRadius.circular(12.r),
        decoration: InputDecoration(
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.r),
            borderSide: BorderSide(color: borderColor ?? AppColors.black010101, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.r),
            borderSide: BorderSide(color: borderColor ?? AppColors.black010101, width: 1.5),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.r),
            borderSide: BorderSide(color: AppColors.redF94008, width: 1),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(100.r),
            borderSide: BorderSide(color: AppColors.redF94008, width: 1.5),
          ),
          prefixIcon:
              prefixIcon != null
                  ? Icon(prefixIcon, color: textColor ?? AppColors.black).paddingOnly(left: 12.w)
                  : null,
          prefixIconConstraints: BoxConstraints(maxWidth: 30.w),
          filled: true,
          fillColor: AppColors.white,
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 18.h),
        ),
        dropdownColor: dropdownColor ?? AppColors.white,
        padding: EdgeInsets.zero,
        hint: Text(
          hint,
          style:
              hintStyle ??
              TextStyles.regular.copyWith(color: textColor ?? AppColors.black.withAlpha(120)),
        ),
        items:
            items.map((String item) {
              return DropdownMenuItem(
                value: item,
                alignment: AlignmentDirectional.centerStart,
                child: Text(
                  item,
                  style: itemStyle ?? TextStyles.bold.copyWith(color: textColor ?? AppColors.black),
                ).paddingOnly(right: 16.w),
              );
            }).toList(),
        onChanged: onChanged,
        validator: validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
      ),
    );
  }
}

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:dateme/ui/utils/const/app_constants.dart';

class ChatSdkManager {
  ChatSdkManager._privateConstructor();

  static final ChatSdkManager instance = ChatSdkManager._privateConstructor();

  static const int orgName = 611337617;
  static const int appName = 1560053;

  /// Initialize the chat SDK
  Future<void> initializeAgoraChat() async {
    try {
      await ChatClient.getInstance.init(ChatOptions(appKey: '$orgName#$appName', autoLogin: false));
      await ChatClient.getInstance.startCallback();
    } on Exception catch (e) {
      showLog('Getting Error While Initialization $e');
    }
  }

  /// Login user
  void loginUser(String userId, String password) async {
    try {
      await ChatClient.getInstance.loginWithToken(userId, password);
      print('Login success');
    } catch (e) {
      print('Login failed: $e');
    }
  }

  /// Logout user
  void logOut() async {
    try {
      await ChatClient.getInstance.logout(true);
      showLog('sign out succeed');
    } on ChatError catch (e) {
      showLog('sign out failed, code: ${e.code}, desc: ${e.description}');
    }
  }

  /// Send Message
  Future<void> sendMessage(String chatId, String messageContent) async {
    try {
      // List<ChatConversation> conversations = await ChatClient.getInstance.chatManager.getConversation(conversationId);

      if (messageContent == '') {
        return;
      }

      var msg = ChatMessage.createSendMessage(body: ChatTextMessageBody(content: messageContent), to: chatId, chatType: ChatType.Chat);

      showLog('msg msg msg msg $msg');

      await ChatClient.getInstance.chatManager.sendMessage(msg);
    } on Exception catch (e) {
      showLog('Send message error $e');
    }
  }

  Future<void> getMessageList(String chatId) async {
    try {
      ChatConversation? messages = await ChatClient.getInstance.chatManager.getConversation(chatId, createIfNeed: true);
      showLog('Messages: ${messages?.messagesCount()}');
      List<ChatConversation> avc = await ChatClient.getInstance.chatManager.loadAllConversations();
      showLog('avc avc avc avc avc ${avc.length}');

      List<ChatMessage>? loadMsgs = await messages?.loadMessages(loadCount: 20);

      showLog('loadMsgs √ loadMsgs ${loadMsgs?.length}');

      loadMsgs?.forEach((element) {
        showLog('check laod message ${element.body}');
      });

      int? msg = await messages?.messagesCount();

      showLog('msgmsg $msg');
    } catch (e) {
      showLog('Error fetching messages: $e');
    }
  }
}

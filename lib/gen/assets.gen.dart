/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsAnimationGen {
  const $AssetsAnimationGen();

  /// File path: assets/animation/app_logo.json
  String get appLogo => 'assets/animation/app_logo.json';

  /// File path: assets/animation/celebration_animation.json
  String get celebrationAnimation =>
      'assets/animation/celebration_animation.json';

  /// List of all assets
  List<String> get values => [appLogo, celebrationAnimation];
}

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Arima-Bold.ttf
  String get arimaBold => 'assets/fonts/Arima-Bold.ttf';

  /// File path: assets/fonts/Arima-ExtraLight.ttf
  String get arimaExtraLight => 'assets/fonts/Arima-ExtraLight.ttf';

  /// File path: assets/fonts/Arima-Light.ttf
  String get arimaLight => 'assets/fonts/Arima-Light.ttf';

  /// File path: assets/fonts/Arima-Medium.ttf
  String get arimaMedium => 'assets/fonts/Arima-Medium.ttf';

  /// File path: assets/fonts/Arima-Regular.ttf
  String get arimaRegular => 'assets/fonts/Arima-Regular.ttf';

  /// File path: assets/fonts/Arima-SemiBold.ttf
  String get arimaSemiBold => 'assets/fonts/Arima-SemiBold.ttf';

  /// File path: assets/fonts/Arima-Thin.ttf
  String get arimaThin => 'assets/fonts/Arima-Thin.ttf';

  /// List of all assets
  List<String> get values => [
    arimaBold,
    arimaExtraLight,
    arimaLight,
    arimaMedium,
    arimaRegular,
    arimaSemiBold,
    arimaThin,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/chat_calls.png
  AssetGenImage get chatCalls =>
      const AssetGenImage('assets/images/chat_calls.png');

  /// File path: assets/images/forgot_password.json
  String get forgotPassword => 'assets/images/forgot_password.json';

  /// File path: assets/images/ic_aadhar_placeholder.png
  AssetGenImage get icAadharPlaceholder =>
      const AssetGenImage('assets/images/ic_aadhar_placeholder.png');

  /// File path: assets/images/ic_chat.png
  AssetGenImage get icChat => const AssetGenImage('assets/images/ic_chat.png');

  /// File path: assets/images/ic_home.png
  AssetGenImage get icHome => const AssetGenImage('assets/images/ic_home.png');

  /// File path: assets/images/ic_notification.png
  AssetGenImage get icNotification =>
      const AssetGenImage('assets/images/ic_notification.png');

  /// File path: assets/images/ic_profile.png
  AssetGenImage get icProfile =>
      const AssetGenImage('assets/images/ic_profile.png');

  /// File path: assets/images/ic_selected_chat.png
  AssetGenImage get icSelectedChat =>
      const AssetGenImage('assets/images/ic_selected_chat.png');

  /// File path: assets/images/ic_selected_home.png
  AssetGenImage get icSelectedHome =>
      const AssetGenImage('assets/images/ic_selected_home.png');

  /// File path: assets/images/ic_selected_notification.png
  AssetGenImage get icSelectedNotification =>
      const AssetGenImage('assets/images/ic_selected_notification.png');

  /// File path: assets/images/ic_selected_profile.png
  AssetGenImage get icSelectedProfile =>
      const AssetGenImage('assets/images/ic_selected_profile.png');

  /// File path: assets/images/ic_selected_subscription.png
  AssetGenImage get icSelectedSubscription =>
      const AssetGenImage('assets/images/ic_selected_subscription.png');

  /// File path: assets/images/ic_subscription.png
  AssetGenImage get icSubscription =>
      const AssetGenImage('assets/images/ic_subscription.png');

  /// File path: assets/images/match_making.png
  AssetGenImage get matchMaking =>
      const AssetGenImage('assets/images/match_making.png');

  /// File path: assets/images/otp_verification.json
  String get otpVerification => 'assets/images/otp_verification.json';

  /// File path: assets/images/real_people.png
  AssetGenImage get realPeople =>
      const AssetGenImage('assets/images/real_people.png');

  /// List of all assets
  List<dynamic> get values => [
    chatCalls,
    forgotPassword,
    icAadharPlaceholder,
    icChat,
    icHome,
    icNotification,
    icProfile,
    icSelectedChat,
    icSelectedHome,
    icSelectedNotification,
    icSelectedProfile,
    icSelectedSubscription,
    icSubscription,
    matchMaking,
    otpVerification,
    realPeople,
  ];
}

class $AssetsSvgsGen {
  const $AssetsSvgsGen();

  /// File path: assets/svgs/chat.svg
  String get chat => 'assets/svgs/chat.svg';

  /// File path: assets/svgs/connection.svg
  String get connection => 'assets/svgs/connection.svg';

  /// File path: assets/svgs/find_your_perfect_match.svg
  String get findYourPerfectMatch => 'assets/svgs/find_your_perfect_match.svg';

  /// File path: assets/svgs/home.svg
  String get home => 'assets/svgs/home.svg';

  /// File path: assets/svgs/profile.svg
  String get profile => 'assets/svgs/profile.svg';

  /// File path: assets/svgs/svg_app_icon.svg
  String get svgAppIcon => 'assets/svgs/svg_app_icon.svg';

  /// File path: assets/svgs/svg_call.svg
  String get svgCall => 'assets/svgs/svg_call.svg';

  /// File path: assets/svgs/svg_camera.svg
  String get svgCamera => 'assets/svgs/svg_camera.svg';

  /// File path: assets/svgs/svg_camera_grey.svg
  String get svgCameraGrey => 'assets/svgs/svg_camera_grey.svg';

  /// File path: assets/svgs/svg_chat.svg
  String get svgChat => 'assets/svgs/svg_chat.svg';

  /// File path: assets/svgs/svg_id.svg
  String get svgId => 'assets/svgs/svg_id.svg';

  /// File path: assets/svgs/svg_id_grey.svg
  String get svgIdGrey => 'assets/svgs/svg_id_grey.svg';

  /// File path: assets/svgs/svg_info.svg
  String get svgInfo => 'assets/svgs/svg_info.svg';

  /// File path: assets/svgs/svg_like.svg
  String get svgLike => 'assets/svgs/svg_like.svg';

  /// File path: assets/svgs/svg_like_empty.svg
  String get svgLikeEmpty => 'assets/svgs/svg_like_empty.svg';

  /// File path: assets/svgs/svg_send.svg
  String get svgSend => 'assets/svgs/svg_send.svg';

  /// File path: assets/svgs/svg_trulynk.svg
  String get svgTrulynk => 'assets/svgs/svg_trulynk.svg';

  /// File path: assets/svgs/svg_varified.svg
  String get svgVarified => 'assets/svgs/svg_varified.svg';

  /// File path: assets/svgs/svg_video_call.svg
  String get svgVideoCall => 'assets/svgs/svg_video_call.svg';

  /// File path: assets/svgs/svg_wallet.svg
  String get svgWallet => 'assets/svgs/svg_wallet.svg';

  /// File path: assets/svgs/wallet.svg
  String get wallet => 'assets/svgs/wallet.svg';

  /// List of all assets
  List<String> get values => [
    chat,
    connection,
    findYourPerfectMatch,
    home,
    profile,
    svgAppIcon,
    svgCall,
    svgCamera,
    svgCameraGrey,
    svgChat,
    svgId,
    svgIdGrey,
    svgInfo,
    svgLike,
    svgLikeEmpty,
    svgSend,
    svgTrulynk,
    svgVarified,
    svgVideoCall,
    svgWallet,
    wallet,
  ];
}

class Assets {
  const Assets._();

  static const $AssetsAnimationGen animation = $AssetsAnimationGen();
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSvgsGen svgs = $AssetsSvgsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
